{"openapi": "3.1.0", "info": {"title": "FastAPI", "version": "0.1.0"}, "paths": {"/v1/products": {"get": {"summary": "Get Products By Sku", "operationId": "get_products_by_sku_v1_products_get", "parameters": [{"description": "Latitude", "required": false, "schema": {"type": "number", "title": "Lat", "description": "Latitude"}, "name": "lat", "in": "query"}, {"description": "Longitude", "required": false, "schema": {"type": "number", "title": "Lng", "description": "Longitude"}, "name": "lng", "in": "query"}, {"description": "<PERSON>", "required": false, "schema": {"type": "number", "title": "Distance", "description": "<PERSON>"}, "name": "distance", "in": "query"}, {"description": "List of states, at least one required", "required": true, "schema": {"items": {"type": "string"}, "type": "array", "title": "States", "description": "List of states, at least one required"}, "name": "states", "in": "query"}, {"description": "List of retailer IDs", "required": false, "schema": {"items": {"type": "integer"}, "type": "array", "title": "Retailers", "description": "List of retailer IDs"}, "name": "retailers", "in": "query"}, {"description": "List of brand IDs", "required": false, "schema": {"items": {"type": "integer"}, "type": "array", "title": "Brands", "description": "List of brand IDs"}, "name": "brands", "in": "query"}, {"description": "Page number, starting from 1", "required": false, "schema": {"type": "integer", "minimum": 1.0, "title": "Page", "description": "Page number, starting from 1", "default": 1}, "name": "page", "in": "query"}, {"description": "Cann SKU IDs", "required": false, "schema": {"items": {"type": "string"}, "type": "array", "title": "Skus", "description": "Cann SKU IDs"}, "name": "skus", "in": "query"}, {"description": "Search query across multiple fields", "required": false, "schema": {"type": "string", "title": "Q", "description": "Search query across multiple fields"}, "name": "q", "in": "query"}, {"description": "Brand Name", "required": false, "schema": {"type": "string", "title": "Brand Name", "description": "Brand Name"}, "name": "brand_name", "in": "query"}, {"description": "Product Name", "required": false, "schema": {"type": "string", "title": "Product Name", "description": "Product Name"}, "name": "product_name", "in": "query"}, {"description": "Display Weight", "required": false, "schema": {"type": "string", "title": "Display Weight", "description": "Display Weight"}, "name": "display_weight", "in": "query"}, {"description": "Category", "required": false, "schema": {"type": "string", "title": "Category", "description": "Category"}, "name": "category", "in": "query"}, {"description": "Subcategory", "required": false, "schema": {"type": "string", "title": "Subcategory", "description": "Subcategory"}, "name": "subcategory", "in": "query"}, {"description": "Product Tags", "required": false, "schema": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "Product Tags"}, "name": "tags", "in": "query"}, {"description": "Percentage THC", "required": false, "schema": {"type": "number", "title": "Percentage Thc", "description": "Percentage THC"}, "name": "percentage_thc", "in": "query"}, {"description": "Percentage CBD", "required": false, "schema": {"type": "number", "title": "Percentage Cbd", "description": "Percentage CBD"}, "name": "percentage_cbd", "in": "query"}, {"description": "Mg THC", "required": false, "schema": {"type": "number", "title": "Mg Thc", "description": "Mg THC"}, "name": "mg_thc", "in": "query"}, {"description": "Mg CBD", "required": false, "schema": {"type": "number", "title": "Mg Cbd", "description": "Mg CBD"}, "name": "mg_cbd", "in": "query"}, {"description": "Quantity Per Package", "required": false, "schema": {"type": "integer", "title": "Quantity Per Package", "description": "Quantity Per Package"}, "name": "quantity_per_package", "in": "query"}, {"description": "Medical", "required": false, "schema": {"type": "boolean", "title": "Medical", "description": "Medical"}, "name": "medical", "in": "query"}, {"description": "Recreational", "required": false, "schema": {"type": "boolean", "title": "Recreational", "description": "Recreational"}, "name": "recreational", "in": "query"}, {"description": "Latest Price", "required": false, "schema": {"type": "number", "title": "Latest Price", "description": "Latest Price"}, "name": "latest_price", "in": "query"}, {"description": "<PERSON><PERSON> Provider", "required": false, "schema": {"type": "string", "title": "<PERSON><PERSON> Provider", "description": "<PERSON><PERSON> Provider"}, "name": "menu_provider", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatedProductsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/products/meta": {"get": {"summary": "Get Product By Meta Sku", "operationId": "get_product_by_meta_sku_v1_products_meta_get", "parameters": [{"description": "Latitude", "required": false, "schema": {"type": "number", "title": "Lat", "description": "Latitude"}, "name": "lat", "in": "query"}, {"description": "Longitude", "required": false, "schema": {"type": "number", "title": "Lng", "description": "Longitude"}, "name": "lng", "in": "query"}, {"description": "<PERSON>", "required": false, "schema": {"type": "number", "title": "Distance", "description": "<PERSON>"}, "name": "distance", "in": "query"}, {"description": "List of states, at least one required", "required": true, "schema": {"items": {"type": "string"}, "type": "array", "title": "States", "description": "List of states, at least one required"}, "name": "states", "in": "query"}, {"description": "List of retailer IDs", "required": false, "schema": {"items": {"type": "integer"}, "type": "array", "title": "Retailers", "description": "List of retailer IDs"}, "name": "retailers", "in": "query"}, {"description": "List of brand IDs", "required": false, "schema": {"items": {"type": "integer"}, "type": "array", "title": "Brands", "description": "List of brand IDs"}, "name": "brands", "in": "query"}, {"description": "Page number, starting from 1", "required": false, "schema": {"type": "integer", "minimum": 1.0, "title": "Page", "description": "Page number, starting from 1", "default": 1}, "name": "page", "in": "query"}, {"description": "Product UUIDs", "required": false, "schema": {"items": {"type": "string"}, "type": "array", "title": "Uuids", "description": "Product UUIDs"}, "name": "uuids", "in": "query"}, {"description": "Brand Name", "required": false, "schema": {"type": "string", "title": "Brand Name", "description": "Brand Name"}, "name": "brand_name", "in": "query"}, {"description": "Product Name", "required": false, "schema": {"type": "string", "title": "Product Name", "description": "Product Name"}, "name": "product_name", "in": "query"}, {"description": "Display Weight", "required": false, "schema": {"type": "string", "title": "Display Weight", "description": "Display Weight"}, "name": "display_weight", "in": "query"}, {"description": "Category", "required": false, "schema": {"type": "string", "title": "Category", "description": "Category"}, "name": "category", "in": "query"}, {"description": "Subcategory", "required": false, "schema": {"type": "string", "title": "Subcategory", "description": "Subcategory"}, "name": "subcategory", "in": "query"}, {"description": "Product Tags", "required": false, "schema": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "Product Tags"}, "name": "tags", "in": "query"}, {"description": "Percentage THC", "required": false, "schema": {"type": "number", "title": "Percentage Thc", "description": "Percentage THC"}, "name": "percentage_thc", "in": "query"}, {"description": "Percentage CBD", "required": false, "schema": {"type": "number", "title": "Percentage Cbd", "description": "Percentage CBD"}, "name": "percentage_cbd", "in": "query"}, {"description": "Mg THC", "required": false, "schema": {"type": "number", "title": "Mg Thc", "description": "Mg THC"}, "name": "mg_thc", "in": "query"}, {"description": "Mg CBD", "required": false, "schema": {"type": "number", "title": "Mg Cbd", "description": "Mg CBD"}, "name": "mg_cbd", "in": "query"}, {"description": "Quantity Per Package", "required": false, "schema": {"type": "integer", "title": "Quantity Per Package", "description": "Quantity Per Package"}, "name": "quantity_per_package", "in": "query"}, {"description": "Medical", "required": false, "schema": {"type": "boolean", "title": "Medical", "description": "Medical"}, "name": "medical", "in": "query"}, {"description": "Recreational", "required": false, "schema": {"type": "boolean", "title": "Recreational", "description": "Recreational"}, "name": "recreational", "in": "query"}, {"description": "Latest Price", "required": false, "schema": {"type": "number", "title": "Latest Price", "description": "Latest Price"}, "name": "latest_price", "in": "query"}, {"description": "<PERSON><PERSON> Provider", "required": false, "schema": {"type": "string", "title": "<PERSON><PERSON> Provider", "description": "<PERSON><PERSON> Provider"}, "name": "menu_provider", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatedProductsResponseMetaSku"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/retailers": {"get": {"summary": "Search Retailers", "operationId": "search_retailers_v1_retailers_get", "parameters": [{"description": "Dispensary ID", "required": false, "schema": {"type": "integer", "title": "Id", "description": "Dispensary ID"}, "name": "id", "in": "query"}, {"description": "Dispensary name", "required": false, "schema": {"type": "string", "title": "Name", "description": "Dispensary name"}, "name": "name", "in": "query"}, {"description": "City", "required": false, "schema": {"type": "string", "title": "City", "description": "City"}, "name": "city", "in": "query"}, {"description": "State", "required": false, "schema": {"type": "string", "title": "State", "description": "State"}, "name": "state", "in": "query"}, {"description": "Zip code", "required": false, "schema": {"type": "string", "title": "Zipcode", "description": "Zip code"}, "name": "zipcode", "in": "query"}, {"description": "Active status", "required": false, "schema": {"type": "boolean", "title": "Is Active", "description": "Active status"}, "name": "is_active", "in": "query"}, {"description": "Serves medical users", "required": false, "schema": {"type": "boolean", "title": "Is Medical", "description": "Serves medical users"}, "name": "is_medical", "in": "query"}, {"description": "Serves recreational users", "required": false, "schema": {"type": "boolean", "title": "Is Recreational", "description": "Serves recreational users"}, "name": "is_recreational", "in": "query"}, {"description": "Offers delivery", "required": false, "schema": {"type": "boolean", "title": "Delivery Enabled", "description": "Offers delivery"}, "name": "delivery_enabled", "in": "query"}, {"description": "Offers pickup", "required": false, "schema": {"type": "boolean", "title": "Pickup Enabled", "description": "Offers pickup"}, "name": "pickup_enabled", "in": "query"}, {"description": "Latitude for proximity search", "required": false, "schema": {"type": "number", "title": "Lat", "description": "Latitude for proximity search"}, "name": "lat", "in": "query"}, {"description": "Longitude for proximity search", "required": false, "schema": {"type": "number", "title": "Lng", "description": "Longitude for proximity search"}, "name": "lng", "in": "query"}, {"description": "Distance in miles for proximity search", "required": false, "schema": {"type": "number", "title": "Distance", "description": "Distance in miles for proximity search"}, "name": "distance", "in": "query"}, {"description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1.0, "title": "Page", "description": "Page number", "default": 1}, "name": "page", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DispensariesResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/brands": {"get": {"summary": "Search Brands", "operationId": "search_brands_v1_brands_get", "parameters": [{"description": "Brand ID", "required": false, "schema": {"type": "integer", "title": "Id", "description": "Brand ID"}, "name": "id", "in": "query"}, {"description": "Brand name", "required": false, "schema": {"type": "string", "title": "Name", "description": "Brand name"}, "name": "name", "in": "query"}, {"description": "Page number", "required": false, "schema": {"type": "integer", "minimum": 1.0, "title": "Page", "description": "Page number", "default": 1}, "name": "page", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BrandsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"BrandSchema": {"properties": {"id": {"type": "integer", "title": "Id"}, "brand_name": {"type": "string", "title": "Brand Name"}, "brand_name_aliases": {"items": {"type": "string"}, "type": "array", "title": "Brand Name Aliases"}, "aproperhigh_brand_website": {"type": "string", "title": "Aproperhigh Brand Website"}, "aproperhigh_slug": {"type": "string", "title": "Aproperhigh Slug"}, "aproperhigh_logo_url": {"type": "string", "title": "Aproperhigh Logo Url"}, "aproperhigh_brand_logo_cann_s3_url": {"type": "string", "title": "Aproperhigh Brand Logo Cann S3 Url"}, "aproperhigh_twitter_profile_url": {"type": "string", "title": "Aproperhigh Twitter Profile Url"}, "aproperhigh_instagram_profile_url": {"type": "string", "title": "Aproperhigh Instagram Profile Url"}, "aproperhigh_facebook_profile_url": {"type": "string", "title": "Aproperhigh Facebook Profile Url"}, "aproperhigh_linkedin_profile_url": {"type": "string", "title": "Aproperhigh Linkedin Profile Url"}, "aproperhigh_description": {"type": "string", "title": "Aproperhigh Description"}, "aproperhigh_regions": {"items": {"type": "string"}, "type": "array", "title": "Aproperhigh Regions"}, "hubspot_id": {"type": "string", "title": "Hubspot Id"}, "leafly_brand_logo_cann_s3_url": {"type": "string", "title": "Leafly Brand Logo Cann S3 Url"}, "leafly_brand_website": {"type": "string", "title": "Leafly Brand Website"}, "leafly_slug": {"type": "string", "title": "<PERSON><PERSON>lug"}, "preferred_brand_name": {"type": "string", "title": "Preferred Brand Name"}, "preferred_image_url": {"type": "string", "title": "Preferred Image Url"}, "preferred_website": {"type": "string", "title": "Preferred Website"}, "weedmaps_brand_logo_cann_s3_url": {"type": "string", "title": "Weedmaps Brand Logo Cann S3 Url"}, "weedmaps_slug": {"type": "string", "title": "Weedmaps Slug"}, "wikileaf_brand_logo_cann_s3_url": {"type": "string", "title": "Wikileaf Brand Logo Cann S3 Url"}, "wikileaf_brand_website": {"type": "string", "title": "Wikileaf Brand Website"}, "wikileaf_instagram_profile_url": {"type": "string", "title": "Wikileaf Instagram Profile Url"}, "wikileaf_slug": {"type": "string", "title": "Wikileaf Slug"}, "wikileaf_twitter_profile_url": {"type": "string", "title": "Wikileaf Twitter Profile Url"}, "dutchie_brand_id": {"type": "string", "title": "<PERSON><PERSON> Id"}, "dutchie_brand_logo_cann_s3_url": {"type": "string", "title": "<PERSON>ie Brand Logo Cann S3 Url"}}, "type": "object", "required": ["id", "brand_name"], "title": "BrandSchema"}, "BrandsResponse": {"properties": {"data": {"items": {"$ref": "#/components/schemas/BrandSchema"}, "type": "array", "title": "Data"}, "pagination": {"type": "object", "title": "Pagination"}}, "type": "object", "required": ["data", "pagination"], "title": "BrandsResponse", "example": {"data": [{"id": 1, "brand_name": "Example Brand", "brand_name_aliases": ["Ex Brand", "EB"], "aproperhigh_brand_website": "https://example.com", "aproperhigh_slug": "example-brand", "aproperhigh_logo_url": "https://example.com/logo.png"}], "pagination": {"total_records": 100, "current_page": 1, "total_pages": 5, "next_page": 2}}}, "DispensariesResponse": {"properties": {"data": {"items": {"$ref": "#/components/schemas/PartnerDispensarySchema"}, "type": "array", "title": "Data", "description": "List of dispensaries"}, "pagination": {"$ref": "#/components/schemas/Pagination"}}, "type": "object", "required": ["data", "pagination"], "title": "DispensariesResponse", "example": {"data": [{"dispensary_id": "1234", "dispensary_name": "Green Leaf Dispensary", "is_active": true, "cann_dispensary_slug": "green-leaf-dispensary", "website_url": "https://greenleaf.com", "contact_phone": "+**********", "contact_email": "<EMAIL>", "physical_address": "123 Main St, Anytown, AN 12345", "city": "Anytown", "state": "AN", "zip_code": "12345", "country": "USA", "latitude": 40.7128, "longitude": -74.006, "serves_medical_users": true, "serves_recreational_users": true, "payment_methods": {"cash_only": false, "credit_card": {"accepted": true, "by_phone": false}, "debit_card": true, "digital_payments": {"canpay": true}}, "online_services_offered": {"delivery": true, "pickup": true}, "ratings": {"average": 4.5}, "reviews_count": {"total": 100}, "brand_ids": [1, 2, 3]}], "pagination": {"total_records": 100, "current_page": 1, "total_pages": 5, "next_page": 2}}}, "GroupedProduct": {"properties": {"retailer_id": {"type": "string", "title": "Retailer Id"}, "sku": {"type": "string", "title": "S<PERSON>"}, "products": {"items": {"$ref": "#/components/schemas/PartnerProductSchema"}, "type": "array", "title": "Products"}}, "type": "object", "required": ["retailer_id", "sku", "products"], "title": "GroupedProduct"}, "GroupedProductMetaSku": {"properties": {"retailer_id": {"type": "string", "title": "Retailer Id"}, "meta_sku": {"type": "string", "title": "<PERSON><PERSON> S<PERSON>"}, "products": {"items": {"$ref": "#/components/schemas/PartnerProductSchema"}, "type": "array", "title": "Products"}}, "type": "object", "required": ["retailer_id", "meta_sku", "products"], "title": "GroupedProductMetaSku"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Pagination": {"properties": {"total_records": {"type": "integer", "title": "Total Records", "description": "Total number of unique dispensary-SKU pairs"}, "current_page": {"type": "integer", "title": "Current Page", "description": "Current page number"}, "total_pages": {"type": "integer", "title": "Total Pages", "description": "Total number of pages"}, "next_page": {"type": "integer", "title": "Next Page", "description": "Next page number if available"}, "prev_page": {"type": "integer", "title": "Prev Page", "description": "Previous page number if available"}}, "type": "object", "required": ["total_records", "current_page", "total_pages"], "title": "Pagination", "example": {"total_records": 1000, "current_page": 1, "total_pages": 10, "next_page": 2}}, "PartnerDispensarySchema": {"properties": {"id": {"type": "integer", "title": "Id"}, "dispensary_name": {"type": "string", "title": "Dispensary Name"}, "is_active": {"type": "boolean", "title": "Is Active"}, "cann_dispensary_slug": {"type": "string", "title": "Cann Dispensary Slug"}, "website_url": {"type": "string", "title": "Website Url"}, "contact_phone": {"type": "string", "title": "Contact Phone"}, "contact_email": {"type": "string", "title": "Contact Email"}, "physical_address": {"type": "string", "title": "Physical Address"}, "city": {"type": "string", "title": "City"}, "state": {"type": "string", "title": "State"}, "zip_code": {"type": "string", "title": "Zip Code"}, "country": {"type": "string", "title": "Country"}, "latitude": {"type": "number", "title": "Latitude"}, "longitude": {"type": "number", "title": "Longitude"}, "serves_medical_users": {"type": "boolean", "title": "Serves Medical Users"}, "serves_recreational_users": {"type": "boolean", "title": "Serves Recreational Users"}, "payment_methods": {"type": "object", "title": "Payment Methods"}, "online_services_offered": {"type": "object", "title": "Online Services Offered"}, "ratings": {"type": "object", "title": "Ratings"}, "reviews_count": {"type": "object", "title": "Reviews Count"}, "brand_ids": {"items": {"type": "integer"}, "type": "array", "title": "Brand Ids"}}, "type": "object", "required": ["id"], "title": "PartnerDispensarySchema"}, "PartnerProductSchema": {"properties": {"cann_sku_id": {"type": "string", "title": "Cann Sku Id"}, "brand_name": {"type": "string", "title": "Brand Name"}, "brand_id": {"type": "integer", "title": "Brand Id"}, "url": {"type": "string", "title": "Url"}, "image_url": {"type": "string", "title": "Image Url"}, "raw_product_name": {"type": "string", "title": "Raw Product Name"}, "product_name": {"type": "string", "title": "Product Name"}, "raw_weight_string": {"type": "string", "title": "Raw Weight String"}, "display_weight": {"type": "string", "title": "Display Weight"}, "raw_product_category": {"type": "string", "title": "Raw Product Category"}, "category": {"type": "string", "title": "Category"}, "raw_subcategory": {"type": "string", "title": "Raw Subcategory"}, "subcategory": {"type": "string", "title": "Subcategory"}, "product_tags": {"items": {"type": "string"}, "type": "array", "title": "Product Tags"}, "percentage_thc": {"type": "number", "title": "Percentage Thc"}, "percentage_cbd": {"type": "number", "title": "Percentage Cbd"}, "mg_thc": {"type": "number", "title": "Mg Thc"}, "mg_cbd": {"type": "number", "title": "Mg Cbd"}, "quantity_per_package": {"type": "integer", "title": "Quantity Per Package"}, "medical": {"type": "boolean", "title": "Medical"}, "recreational": {"type": "boolean", "title": "Recreational"}, "latest_price": {"type": "number", "title": "Latest Price"}, "original_price": {"type": "number", "title": "Original Price"}, "menu_provider": {"type": "string", "title": "<PERSON><PERSON> Provider"}}, "type": "object", "title": "PartnerProductSchema", "example": {"cann_sku_id": "123456", "brand_name": "Brand X", "raw_product_name": "Product X", "product_name": "Product X", "raw_weight_string": "1g", "display_weight": "1g", "raw_product_category": "Flower", "category": "Flower", "raw_subcategory": "Indica", "subcategory": "Indica", "product_tags": ["Tag1", "Tag2"], "percentage_thc": 20.5, "percentage_cbd": 0.5, "mg_thc": 205.0, "mg_cbd": 5.0, "quantity_per_package": 1, "medical": true, "recreational": false, "latest_price": 10.0, "original_price": 12.0, "menu_provider": "Provider X"}}, "UpdatedProductsResponse": {"properties": {"data": {"items": {"$ref": "#/components/schemas/GroupedProduct"}, "type": "array", "title": "Data"}, "pagination": {"$ref": "#/components/schemas/Pagination"}}, "type": "object", "required": ["data", "pagination"], "title": "UpdatedProductsResponse"}, "UpdatedProductsResponseMetaSku": {"properties": {"data": {"items": {"$ref": "#/components/schemas/GroupedProductMetaSku"}, "type": "array", "title": "Data"}, "pagination": {"$ref": "#/components/schemas/Pagination"}}, "type": "object", "required": ["data", "pagination"], "title": "UpdatedProductsResponseMetaSku"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}