/**
 * Data migration to move first_name and last_name from JSON data field to direct columns
 * This migration is safe to run multiple times
 */

exports.up = async function (knex) {
  const batchSize = 1000;
  let offset = 0;
  let hasMoreUsers = true;

  console.log(
    "Starting migration of user name fields from JSON to direct columns..."
  );

  while (hasMoreUsers) {
    // Get batch of users with data that contains name fields
    const users = await knex("users")
      .whereRaw(
        "JSON_EXTRACT(data, '$.first_name') IS NOT NULL OR JSON_EXTRACT(data, '$.firstName') IS NOT NULL OR JSON_EXTRACT(data, '$.last_name') IS NOT NULL OR JSON_EXTRACT(data, '$.lastName') IS NOT NULL"
      )
      .andWhere(function () {
        // Only migrate users where the direct columns are null (haven't been migrated yet)
        this.whereNull("first_name").orWhereNull("last_name");
      })
      .limit(batchSize)
      .offset(offset);

    if (users.length === 0) {
      hasMoreUsers = false;
      break;
    }

    console.log(
      `Processing batch ${Math.floor(offset / batchSize) + 1}: ${
        users.length
      } users`
    );

    // Process each user in the batch
    for (const user of users) {
      let data = {};
      try {
        data =
          typeof user.data === "string"
            ? JSON.parse(user.data)
            : user.data || {};
      } catch (e) {
        console.warn(
          `Failed to parse JSON data for user ${user.id}:`,
          e.message
        );
        continue;
      }

      // Extract name fields with fallbacks
      const firstName = data.first_name || data.firstName || data.name;
      const lastName = data.last_name || data.lastName || data.surname;

      // Only update if we have at least one name field and it's not already set
      if ((firstName || lastName) && (!user.first_name || !user.last_name)) {
        const updateData = {};

        if (firstName && !user.first_name) {
          updateData.first_name = firstName;
        }

        if (lastName && !user.last_name) {
          updateData.last_name = lastName;
        }

        // Update the user record
        await knex("users").where("id", user.id).update(updateData);
      }
    }

    offset += batchSize;
  }

  console.log("Migration of user name fields completed!");
};

exports.down = async function (knex) {
  // This migration doesn't need to be reversed as it's just copying data
  // The original data remains in the JSON field
  console.log(
    "Down migration for user name fields - no action needed (data preserved in JSON field)"
  );
};
