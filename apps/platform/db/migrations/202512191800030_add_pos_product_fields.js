exports.up = async function (knex) {
  // Define all columns we want to add
  const columnsToAdd = [
    {
      name: "pos_type",
      definition: (table) => table.string("pos_type").nullable(),
    },
    {
      name: "pos_product_id",
      definition: (table) => table.string("pos_product_id").nullable(),
    },
    {
      name: "crb_id",
      definition: (table) => table.string("crb_id").nullable(),
    },
    {
      name: "gc_product_category_name",
      definition: (table) =>
        table.string("gc_product_category_name").nullable(),
    },
    {
      name: "gc_net_weight_grams",
      definition: (table) =>
        table.decimal("gc_net_weight_grams", 10, 3).nullable(),
    },
    {
      name: "gc_created_date",
      definition: (table) => table.timestamp("gc_created_date").nullable(),
    },
    {
      name: "gc_created_date_local",
      definition: (table) =>
        table.timestamp("gc_created_date_local").nullable(),
    },
    {
      name: "pos_unit_of_measure",
      definition: (table) => table.string("pos_unit_of_measure").nullable(),
    },
    {
      name: "price_tiers",
      definition: (table) => table.json("price_tiers").nullable(),
    },
    {
      name: "pos_updated_date",
      definition: (table) => table.timestamp("pos_updated_date").nullable(),
    },
  ];

  // Check which columns don't exist yet
  const existingColumns = await Promise.all(
    columnsToAdd.map(async (col) => ({
      name: col.name,
      exists: await knex.schema.hasColumn("products", col.name),
      definition: col.definition,
    }))
  );

  const columnsToCreate = existingColumns.filter((col) => !col.exists);

  // Add columns that don't exist
  if (columnsToCreate.length > 0) {
    await knex.schema.alterTable("products", function (table) {
      columnsToCreate.forEach((col) => col.definition(table));
    });
  }

  // Define indexes we want to add
  const indexesToAdd = ["pos_product_id", "crb_id", "pos_type"];

  // Add indexes for columns that exist (either were just created or already existed)
  for (const indexName of indexesToAdd) {
    const hasColumn = await knex.schema.hasColumn("products", indexName);
    if (hasColumn) {
      try {
        await knex.schema.alterTable("products", function (table) {
          table.index(indexName);
        });
      } catch (error) {
        // Index might already exist, ignore error
        if (!error.message.includes("Duplicate key name")) {
          throw error;
        }
      }
    }
  }
};

exports.down = async function (knex) {
  // Define indexes to drop
  const indexesToDrop = ["pos_type", "crb_id", "pos_product_id"];

  // Drop indexes if they exist
  for (const indexName of indexesToDrop) {
    try {
      await knex.schema.alterTable("products", function (table) {
        table.dropIndex(indexName);
      });
    } catch (error) {
      // Index might not exist, ignore error
    }
  }

  // Define columns to drop
  const columnsToDrop = [
    "pos_updated_date",
    "price_tiers",
    "pos_unit_of_measure",
    "gc_created_date_local",
    "gc_created_date",
    "gc_net_weight_grams",
    "gc_product_category_name",
    "crb_id",
    "pos_product_id",
    "pos_type",
  ];

  // Check which columns exist before trying to drop them
  const existingColumns = await Promise.all(
    columnsToDrop.map(async (colName) => ({
      name: colName,
      exists: await knex.schema.hasColumn("products", colName),
    }))
  );

  const columnsToRemove = existingColumns.filter((col) => col.exists);

  // Drop columns that exist
  if (columnsToRemove.length > 0) {
    await knex.schema.alterTable("products", function (table) {
      columnsToRemove.forEach((col) => table.dropColumn(col.name));
    });
  }
};
