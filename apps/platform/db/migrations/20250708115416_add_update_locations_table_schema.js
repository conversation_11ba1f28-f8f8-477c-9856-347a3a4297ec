exports.up = async function (knex) {
  const hasCrbId = await knex.schema.hasColumn("locations", "crb_id");
  const hasPosConfigs = await knex.schema.hasColumn("locations", "pos_configs");

  await knex.schema.alterTable("locations", function (table) {
    if (!hasCrbId) {
      table.string("crb_id", 50).nullable();
    }
    if (hasPosConfigs) {
      table.renameColumn("pos_configs", "pos_credentials");
    } else {
      table.json("pos_credentials").nullable();
    }
  });
};

exports.down = async function (knex) {
  const hasCrbId = await knex.schema.hasColumn("locations", "crb_id");
  const hasPosCredentials = await knex.schema.hasColumn("locations", "pos_credentials");

  await knex.schema.alterTable("locations", function (table) {
    if (hasCrbId) {
      table.dropColumn("crb_id");
    }
    if (hasPosCredentials) {
      table.renameColumn("pos_credentials", "pos_configs");
    }
  });
};
