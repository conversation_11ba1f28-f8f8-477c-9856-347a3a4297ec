exports.up = async function (knex) {
  // Define all columns we want to add
  const columnsToAdd = [
    {
      name: "pos_location_id",
      definition: (table) => table.string("pos_location_id").nullable(),
    },
    { name: "ein", definition: (table) => table.string("ein").nullable() },
    { name: "dba", definition: (table) => table.string("dba").nullable() },
    {
      name: "entity_type",
      definition: (table) => table.string("entity_type").nullable(),
    },
    {
      name: "business_type",
      definition: (table) => table.string("business_type").nullable(),
    },
    {
      name: "org_type",
      definition: (table) => table.string("org_type").nullable(),
    },
    {
      name: "established_date",
      definition: (table) => table.date("established_date").nullable(),
    },
    {
      name: "ft_employees",
      definition: (table) =>
        table.integer("ft_employees").nullable().defaultTo(0),
    },
    {
      name: "pt_employees",
      definition: (table) =>
        table.integer("pt_employees").nullable().defaultTo(0),
    },
    {
      name: "monthly_sales",
      definition: (table) => table.string("monthly_sales").nullable(),
    },
    {
      name: "monthly_customers",
      definition: (table) =>
        table.integer("monthly_customers").nullable().defaultTo(0),
    },
    {
      name: "mailing_street_address",
      definition: (table) => table.string("mailing_street_address").nullable(),
    },
    {
      name: "mailing_street_address_2",
      definition: (table) =>
        table.string("mailing_street_address_2").nullable(),
    },
    {
      name: "mailing_city",
      definition: (table) => table.string("mailing_city").nullable(),
    },
    {
      name: "mailing_state",
      definition: (table) => table.string("mailing_state").nullable(),
    },
    {
      name: "mailing_postal_code",
      definition: (table) => table.string("mailing_postal_code").nullable(),
    },
    {
      name: "street_address",
      definition: (table) => table.string("street_address").nullable(),
    },
    {
      name: "street_address_2",
      definition: (table) => table.string("street_address_2").nullable(),
    },
    {
      name: "postal_code",
      definition: (table) => table.string("postal_code").nullable(),
    },
    {
      name: "status",
      definition: (table) => table.string("status").nullable(),
    },
    {
      name: "pos_configs",
      definition: (table) => table.json("pos_configs").nullable(),
    },
    {
      name: "template_id",
      definition: (table) => table.string("template_id").nullable(),
    },
    {
      name: "template_result_id",
      definition: (table) => table.string("template_result_id").nullable(),
    },
  ];

  // Check which columns don't exist yet
  const existingColumns = await Promise.all(
    columnsToAdd.map(async (col) => ({
      name: col.name,
      exists: await knex.schema.hasColumn("locations", col.name),
      definition: col.definition,
    }))
  );

  const columnsToCreate = existingColumns.filter((col) => !col.exists);

  // Add columns that don't exist
  if (columnsToCreate.length > 0) {
    await knex.schema.alterTable("locations", function (table) {
      columnsToCreate.forEach((col) => col.definition(table));
    });
  }

  // Define indexes we want to add
  const indexesToAdd = ["pos_location_id", "ein", "business_type", "status"];

  // Add indexes for columns that exist (either were just created or already existed)
  for (const indexName of indexesToAdd) {
    const hasColumn = await knex.schema.hasColumn("locations", indexName);
    if (hasColumn) {
      try {
        await knex.schema.alterTable("locations", function (table) {
          table.index(indexName);
        });
      } catch (error) {
        // Index might already exist, ignore error
        if (!error.message.includes("Duplicate key name")) {
          throw error;
        }
      }
    }
  }
};

exports.down = async function (knex) {
  // Define indexes to drop
  const indexesToDrop = ["status", "business_type", "ein", "pos_location_id"];

  // Drop indexes if they exist
  for (const indexName of indexesToDrop) {
    try {
      await knex.schema.alterTable("locations", function (table) {
        table.dropIndex(indexName);
      });
    } catch (error) {
      // Index might not exist, ignore error
    }
  }

  // Define columns to drop
  const columnsToDrop = [
    "template_result_id",
    "template_id",
    "pos_configs",
    "status",
    "postal_code",
    "street_address_2",
    "street_address",
    "mailing_postal_code",
    "mailing_state",
    "mailing_city",
    "mailing_street_address_2",
    "mailing_street_address",
    "monthly_customers",
    "monthly_sales",
    "pt_employees",
    "ft_employees",
    "established_date",
    "org_type",
    "business_type",
    "entity_type",
    "dba",
    "ein",
    "pos_location_id",
  ];

  // Check which columns exist before trying to drop them
  const existingColumns = await Promise.all(
    columnsToDrop.map(async (colName) => ({
      name: colName,
      exists: await knex.schema.hasColumn("locations", colName),
    }))
  );

  const columnsToRemove = existingColumns.filter((col) => col.exists);

  // Drop columns that exist
  if (columnsToRemove.length > 0) {
    await knex.schema.alterTable("locations", function (table) {
      columnsToRemove.forEach((col) => table.dropColumn(col.name));
    });
  }
};
