/**
 * Fix agent_ids column to ensure all values are properly formatted JSON arrays
 * @param {import('knex').Knex} knex
 * @returns {Promise<void>}
 */
exports.up = async function (knex) {
  // Get all chats with potentially malformed agent_ids
  const chats = await knex('chats').select('id', 'agent_ids');
  
  for (const chat of chats) {
    let fixedAgentIds = [];
    
    if (chat.agent_ids) {
      // If it's already a valid JSON array, keep it
      if (typeof chat.agent_ids === 'object' && Array.isArray(chat.agent_ids)) {
        fixedAgentIds = chat.agent_ids;
      } else if (typeof chat.agent_ids === 'string') {
        try {
          // Try to parse as JSON
          const parsed = JSON.parse(chat.agent_ids);
          if (Array.isArray(parsed)) {
            fixedAgentIds = parsed;
          } else {
            // If it's a single value, wrap it in an array
            fixedAgentIds = [parsed];
          }
        } catch (e) {
          // If <PERSON><PERSON><PERSON> parsing fails, treat the string as a single agent ID
          fixedAgentIds = [chat.agent_ids];
        }
      } else {
        // For any other type, convert to string and wrap in array
        fixedAgentIds = [chat.agent_ids.toString()];
      }
    }
    
    // Update the chat with properly formatted JSON
    await knex('chats')
      .where('id', chat.id)
      .update({
        agent_ids: JSON.stringify(fixedAgentIds),
        updated_at: knex.fn.now()
      });
  }
};

/**
 * @param {import('knex').Knex} knex
 * @returns {Promise<void>}
 */
exports.down = async function (knex) {
  // This migration is a data fix, so we don't need to reverse it
  // The down migration is a no-op
};
