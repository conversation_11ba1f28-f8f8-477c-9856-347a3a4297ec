exports.up = async function (knex) {
  // Check if pos_provider column already exists
  const hasColumn = await knex.schema.hasColumn("locations", "pos_provider");

  if (!hasColumn) {
    await knex.schema.alterTable("locations", function (table) {
      // Add pos_provider field to track which POS system is connected
      table.string("pos_provider").nullable(); // e.g., "dutchie", "greencheck", "marijuanasoftware", etc.

      // Add index for performance
      table.index("pos_provider");
    });
  } else {
    // Column exists, but maybe index doesn't - try to add index safely
    try {
      await knex.schema.alterTable("locations", function (table) {
        table.index("pos_provider");
      });
    } catch (error) {
      // Index might already exist, ignore error
      if (!error.message.includes("Duplicate key name")) {
        throw error;
      }
    }
  }
};

exports.down = async function (knex) {
  // Check if pos_provider column exists before trying to drop it
  const hasColumn = await knex.schema.hasColumn("locations", "pos_provider");

  if (hasColumn) {
    await knex.schema.alterTable("locations", function (table) {
      // Drop index first
      try {
        table.dropIndex("pos_provider");
      } catch (error) {
        // Index might not exist, ignore error
      }

      // Drop column
      table.dropColumn("pos_provider");
    });
  }
};
