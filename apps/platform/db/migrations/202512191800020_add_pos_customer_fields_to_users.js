exports.up = async function (knex) {
  // Define all columns we want to add
  const columnsToAdd = [
    {
      name: "pos_type",
      definition: (table) => table.string("pos_type").nullable(),
    },
    {
      name: "pos_customer_id",
      definition: (table) => table.string("pos_customer_id").nullable(),
    },
    {
      name: "crb_id",
      definition: (table) => table.string("crb_id").nullable(),
    },
    {
      name: "first_name",
      definition: (table) => table.string("first_name").nullable(),
    },
    {
      name: "last_name",
      definition: (table) => table.string("last_name").nullable(),
    },
    {
      name: "middle_name",
      definition: (table) => table.string("middle_name").nullable(),
    },
    {
      name: "address_1",
      definition: (table) => table.string("address_1").nullable(),
    },
    {
      name: "address_2",
      definition: (table) => table.string("address_2").nullable(),
    },
    { name: "city", definition: (table) => table.string("city").nullable() },
    { name: "state", definition: (table) => table.string("state").nullable() },
    {
      name: "zip_code",
      definition: (table) => table.string("zip_code").nullable(),
    },
    {
      name: "cell_phone",
      definition: (table) => table.string("cell_phone").nullable(),
    },
    {
      name: "gender",
      definition: (table) => table.string("gender").nullable(),
    },
    {
      name: "pos_identification_type",
      definition: (table) => table.string("pos_identification_type").nullable(),
    },
    {
      name: "pos_customer_type",
      definition: (table) => table.string("pos_customer_type").nullable(),
    },
    {
      name: "gc_customer_type",
      definition: (table) => table.string("gc_customer_type").nullable(),
    },
    {
      name: "medical_id",
      definition: (table) => table.string("medical_id").nullable(),
    },
    {
      name: "medical_id_expiration_date",
      definition: (table) =>
        table.date("medical_id_expiration_date").nullable(),
    },
    {
      name: "drivers_license_id",
      definition: (table) => table.string("drivers_license_id").nullable(),
    },
    {
      name: "drivers_license_expiration_date",
      definition: (table) =>
        table.date("drivers_license_expiration_date").nullable(),
    },
    {
      name: "other_id",
      definition: (table) => table.string("other_id").nullable(),
    },
    {
      name: "other_id_expiration_date",
      definition: (table) =>
        table.string("other_id_expiration_date").nullable(),
    },
    {
      name: "caregiver",
      definition: (table) =>
        table.boolean("caregiver").nullable().defaultTo(false),
    },
    {
      name: "loyalty_member",
      definition: (table) =>
        table.boolean("loyalty_member").nullable().defaultTo(false),
    },
    {
      name: "anonymous",
      definition: (table) =>
        table.boolean("anonymous").nullable().defaultTo(false),
    },
    {
      name: "loyalty_points",
      definition: (table) =>
        table.decimal("loyalty_points", 15, 6).nullable().defaultTo(0),
    },
    {
      name: "pos_updated_date",
      definition: (table) => table.timestamp("pos_updated_date").nullable(),
    },
    {
      name: "pos_updated_date_local",
      definition: (table) =>
        table.timestamp("pos_updated_date_local").nullable(),
    },
    {
      name: "gc_created_date",
      definition: (table) => table.timestamp("gc_created_date").nullable(),
    },
    {
      name: "gc_created_date_local",
      definition: (table) =>
        table.timestamp("gc_created_date_local").nullable(),
    },
  ];

  // Check which columns don't exist yet
  const existingColumns = await Promise.all(
    columnsToAdd.map(async (col) => ({
      name: col.name,
      exists: await knex.schema.hasColumn("users", col.name),
      definition: col.definition,
    }))
  );

  const columnsToCreate = existingColumns.filter((col) => !col.exists);

  // Add columns that don't exist
  if (columnsToCreate.length > 0) {
    await knex.schema.alterTable("users", function (table) {
      columnsToCreate.forEach((col) => col.definition(table));
    });
  }

  // Define indexes we want to add
  const indexesToAdd = [
    "pos_customer_id",
    "crb_id",
    "medical_id",
    "drivers_license_id",
    "pos_type",
    "pos_customer_type",
  ];

  // Add indexes for columns that exist (either were just created or already existed)
  for (const indexName of indexesToAdd) {
    const hasColumn = await knex.schema.hasColumn("users", indexName);
    if (hasColumn) {
      try {
        await knex.schema.alterTable("users", function (table) {
          table.index(indexName);
        });
      } catch (error) {
        // Index might already exist, ignore error
        if (!error.message.includes("Duplicate key name")) {
          throw error;
        }
      }
    }
  }
};

exports.down = async function (knex) {
  // Define indexes to drop
  const indexesToDrop = [
    "pos_customer_type",
    "pos_type",
    "drivers_license_id",
    "medical_id",
    "crb_id",
    "pos_customer_id",
  ];

  // Drop indexes if they exist
  for (const indexName of indexesToDrop) {
    try {
      await knex.schema.alterTable("users", function (table) {
        table.dropIndex(indexName);
      });
    } catch (error) {
      // Index might not exist, ignore error
    }
  }

  // Define columns to drop
  const columnsToDrop = [
    "gc_created_date_local",
    "gc_created_date",
    "pos_updated_date_local",
    "pos_updated_date",
    "loyalty_points",
    "anonymous",
    "loyalty_member",
    "caregiver",
    "other_id_expiration_date",
    "other_id",
    "drivers_license_expiration_date",
    "drivers_license_id",
    "medical_id_expiration_date",
    "medical_id",
    "gc_customer_type",
    "pos_customer_type",
    "pos_identification_type",
    "gender",
    "cell_phone",
    "zip_code",
    "state",
    "city",
    "address_2",
    "address_1",
    "middle_name",
    "last_name",
    "first_name",
    "crb_id",
    "pos_customer_id",
    "pos_type",
  ];

  // Check which columns exist before trying to drop them
  const existingColumns = await Promise.all(
    columnsToDrop.map(async (colName) => ({
      name: colName,
      exists: await knex.schema.hasColumn("users", colName),
    }))
  );

  const columnsToRemove = existingColumns.filter((col) => col.exists);

  // Drop columns that exist
  if (columnsToRemove.length > 0) {
    await knex.schema.alterTable("users", function (table) {
      columnsToRemove.forEach((col) => table.dropColumn(col.name));
    });
  }
};
