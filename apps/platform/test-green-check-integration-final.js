/**
 * Final Green Check Integration Test - simulates the full workflow
 */

// Load environment variables
require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: '/usr/src/app/.env' });
require('dotenv').config({ path: '/usr/src/app/apps/platform/.env' });

const axios = require('axios');

console.log('🎯 Final Green Check Integration Test...\n');

async function testGreenCheckIntegration() {
  try {
    // Step 1: Verify environment variables
    console.log('📋 Step 1: Environment Variables');
    const clientId = process.env.GREEN_CHECK_CLIENT_ID;
    const clientSecret = process.env.GREEN_CHECK_CLIENT_SECRET;
    const serviceProviderId = process.env.GREEN_CHECK_SERVICE_PROVIDER_ID;
    const baseUrl = process.env.GREEN_CHECK_BASE_URL || 'https://sandbox-api.greencheckverified.com';

    console.log(`   CLIENT_ID: ${clientId ? '✅ Set' : '❌ Missing'}`);
    console.log(`   CLIENT_SECRET: ${clientSecret ? '✅ Set' : '❌ Missing'}`);
    console.log(`   SERVICE_PROVIDER_ID: ${serviceProviderId ? '✅ Set' : '❌ Missing'}`);
    console.log(`   BASE_URL: ${baseUrl}`);

    if (!clientId || !clientSecret || !serviceProviderId) {
      throw new Error('Missing required environment variables');
    }

    // Step 2: Authenticate
    console.log('\n🔐 Step 2: Authentication');
    const authResponse = await axios.post(`${baseUrl}/auth/token`, {
      client_id: clientId,
      client_secret: clientSecret,
      grant_type: 'client_credentials',
      scope: ['service-provider:read', 'point-of-sale:read']
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const accessToken = authResponse.data.access_token;
    console.log('✅ Authentication successful');
    console.log(`   Token type: ${authResponse.data.token_type || 'Bearer'}`);

    // Step 3: Get CRBs (Cannabis Related Businesses)
    console.log('\n🏢 Step 3: Get CRBs');
    const crbResponse = await axios.get(`${baseUrl}/service-providers/${serviceProviderId}/crbs`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    const crbsObject = crbResponse.data;
    const crbsArray = Object.values(crbsObject);
    console.log(`✅ Retrieved ${crbsArray.length} CRBs`);

    if (crbsArray.length > 0) {
      const firstCrb = crbsArray[0];
      console.log(`   First CRB: ${firstCrb.name} (ID: ${firstCrb.id})`);
      console.log(`   Location: ${firstCrb.city}, ${firstCrb.state}`);
      console.log(`   Status: ${firstCrb.status}`);
      console.log(`   POS Systems: ${firstCrb.pos_configs?.length || 0}`);

      // Step 4: Test getting sales data for the first CRB
      console.log('\n📊 Step 4: Get Sales Data');
      try {
        const salesResponse = await axios.get(`${baseUrl}/crbs/${firstCrb.id}/sales`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          params: {
            start_date: '2024-01-01',
            end_date: '2024-12-31'
          }
        });

        console.log('✅ Sales data retrieved successfully');
        console.log(`   Records found: ${Array.isArray(salesResponse.data) ? salesResponse.data.length : 'Unknown'}`);

        if (Array.isArray(salesResponse.data) && salesResponse.data.length > 0) {
          const firstSale = salesResponse.data[0];
          console.log(`   Sample sale: ${JSON.stringify(firstSale, null, 2)}`);
        }

      } catch (salesError) {
        console.log('⚠️  Sales data request failed (this might be expected for demo data):');
        console.log(`   Status: ${salesError.response?.status}`);
        console.log(`   Message: ${salesError.response?.data?.message || salesError.message}`);
      }

      // Step 5: Test getting inventory data
      console.log('\n📦 Step 5: Get Inventory Data');
      try {
        const inventoryResponse = await axios.get(`${baseUrl}/crbs/${firstCrb.id}/inventory`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('✅ Inventory data retrieved successfully');
        console.log(`   Records found: ${Array.isArray(inventoryResponse.data) ? inventoryResponse.data.length : 'Unknown'}`);

      } catch (inventoryError) {
        console.log('⚠️  Inventory data request failed (this might be expected for demo data):');
        console.log(`   Status: ${inventoryError.response?.status}`);
        console.log(`   Message: ${inventoryError.response?.data?.message || inventoryError.message}`);
      }
    }

    console.log('\n🎉 Green Check Integration Test Completed Successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Environment variables loaded');
    console.log('   ✅ Authentication successful');
    console.log('   ✅ CRB data retrieved');
    console.log('   ✅ API endpoints accessible');
    console.log('\n🚀 Green Check integration is ready for use in BakedBot AI!');

  } catch (error) {
    console.log('\n❌ Green Check Integration Test Failed:');
    console.log(`   Error: ${error.message}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

testGreenCheckIntegration();
