import { logger } from "../config/logger";
import { PosData } from "../pos/PosData";
import App from "../app";

export interface OrderToPosData {
  order_id: number;
  location_id: number;
  user_id?: number;
  order_date: Date;
  customer_email?: string;
  customer_phone?: string;
  customer_name?: string;
  customer_id?: string;
  total_amount: number;
  items: Array<{
    product_id: string;
    product_name: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    product_data: any;
  }>;
}

export class OrderToPosService {
  /**
   * Creates POS data entries from a completed order
   * Each order item becomes a separate POS entry
   */
  static async createPosDataFromOrder(
    orderData: OrderToPosData
  ): Promise<void> {
    try {
      logger.info({
        message: "Creating POS data from completed order",
        order_id: orderData.order_id,
        location_id: orderData.location_id,
        items_count: orderData.items.length,
      });

      // Get location information
      const location = await App.main
        .db("locations")
        .where("id", orderData.location_id)
        .first();

      // Get user information for customer data
      let user = null;
      if (orderData.user_id) {
        user = await App.main
          .db("users")
          .where("id", orderData.user_id)
          .first();
      }

      // Extract customer information
      const customerName =
        orderData.customer_name ||
        (user
          ? `${user.data?.first_name || ""} ${
              user.data?.last_name || ""
            }`.trim()
          : "") ||
        user?.email ||
        "Unknown Customer";

      const customerEmail = orderData.customer_email || user?.email || null;
      const customerPhone = orderData.customer_phone || user?.phone || null;
      const customerId = orderData.customer_id || user?.id?.toString() || null;
      const birthDate = user?.birthdate || user?.data?.birthdate || null;

      // Calculate basic order-level values
      const orderLevelData = {
        location_id: orderData.location_id,
        location_name: location?.name || "Unknown Location",
        order_date: orderData.order_date,
        order_id: orderData.order_id,
        customer_name: customerName,
        customer_email: customerEmail,
        customer_phone: customerPhone,
        customer_id: customerId,
        birth_date: birthDate,
        customer_type: "retail", // Default to retail for online orders
        budtender_name: "Online Order", // Default for online orders
        returned_amount: 0, // Default to 0 for new completed orders
        loyalty_as_discount: 0, // TODO: Extract from order if loyalty system exists
        loyalty_as_payment: 0, // TODO: Extract from order if loyalty system exists
        amount_paid_in_cash: 0, // Online orders typically not cash
        amount_paid_in_debit: orderData.total_amount, // Assume card payment for online orders
      };

      // Create a POS entry for each order item
      const posEntries: Partial<PosData>[] = orderData.items.map((item) => {
        // Extract product category from product data
        const masterCategory =
          item.product_data?.category ||
          item.product_data?.raw_product_category ||
          "Unknown";

        // Calculate financial values for this item
        const grossSales = item.total_price;
        const discountedAmount = 0; // TODO: Extract from order discounts if available
        const netSales = grossSales - discountedAmount;

        // Calculate tax (assuming tax is included in total_price for now)
        // TODO: Extract actual tax calculation from order if available
        const taxRate = 0.1; // Default 10% - should be configurable per location
        const taxAmount = grossSales * (taxRate / (1 + taxRate));

        // Calculate inventory costs from product data
        const inventoryCost =
          item.product_data?.wholesale_cost ||
          item.product_data?.cost ||
          grossSales * 0.6; // Default 60% cost ratio if no wholesale cost

        const inventoryProfit = netSales - inventoryCost;
        const profitMargin =
          netSales > 0 ? (inventoryProfit / netSales) * 100 : 0;

        return {
          ...orderLevelData,
          product_name: item.product_name,
          master_category: masterCategory,
          gross_sales: grossSales,
          discounted_amount: discountedAmount,
          net_sales: netSales,
          inventory_cost: inventoryCost,
          inventory_profit: inventoryProfit,
          tax_amount: taxAmount,
          invoice_total: grossSales, // Same as gross_sales for individual items
          wholesale_cost: item.product_data?.wholesale_cost || null,
          profit_margin: profitMargin,
        };
      });

      // Insert all POS entries in a transaction
      await App.main.db.transaction(async (trx) => {
        for (const posEntry of posEntries) {
          await trx("pos_data").insert(posEntry);
        }
      });

      logger.info({
        message: "Successfully created POS data from order",
        order_id: orderData.order_id,
        pos_entries_created: posEntries.length,
      });
    } catch (error) {
      logger.error({
        message: "Error creating POS data from order",
        order_id: orderData.order_id,
        error: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  /**
   * Check if POS data already exists for an order
   */
  static async orderHasPosData(orderId: number): Promise<boolean> {
    const existingEntries = await App.main
      .db("pos_data")
      .where("order_id", orderId)
      .count("id as count")
      .first();

    return parseInt((existingEntries?.count as string) || "0") > 0;
  }

  /**
   * Delete POS data for an order (useful for order cancellations)
   */
  static async deletePosDataForOrder(orderId: number): Promise<void> {
    try {
      await App.main.db("pos_data").where("order_id", orderId).delete();

      logger.info({
        message: "Deleted POS data for order",
        order_id: orderId,
      });
    } catch (error) {
      logger.error({
        message: "Error deleting POS data for order",
        order_id: orderId,
        error: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }
}
