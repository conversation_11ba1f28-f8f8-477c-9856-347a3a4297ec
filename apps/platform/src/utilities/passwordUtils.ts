import crypto from "crypto";

/**
 * Password hashing utility
 */
export class PasswordUtils {
  private static readonly SALT = process.env.APP_SECRET || "default-salt";
  private static readonly ITERATIONS = 100000;
  private static readonly KEY_LENGTH = 64;
  private static readonly DIGEST = "sha512";

  /**
   * Hash a password using PBKDF2
   */
  static hashPassword(password: string): string {
    const hash = crypto.pbkdf2Sync(
      password,
      this.SALT,
      this.ITERATIONS,
      this.KEY_LENGTH,
      this.DIGEST
    );
    return hash.toString("hex");
  }

  /**
   * Verify a password against a hash
   */
  static verifyPassword(password: string, hash: string): boolean {
    const passwordHash = this.hashPassword(password);
    return crypto.timingSafeEqual(
      Buffer.from(passwordHash, "hex"),
      Buffer.from(hash, "hex")
    );
  }

  /**
   * Generate hash for the a given string
   */
  static generatePasswordHash(pwd: string): string {
    return this.hashPassword(pwd);
  }

  /**
   * Test function to verify the password hashing works correctly
   */
  static testPasswordHashing(pwd: string): void {
    const hash = this.hashPassword(pwd);
    const isValid = this.verifyPassword(pwd, hash);
    console.log("Password hash test:", isValid ? "PASSED" : "FAILED");
    console.log("Generated hash:", hash);
  }
}
