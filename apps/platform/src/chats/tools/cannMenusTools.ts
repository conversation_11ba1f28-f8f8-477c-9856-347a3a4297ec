import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { CannMenusApiService } from "../../services/CannMenusApiService";
import { MarketTool } from "../../tools/MarketTool";
import { CompetitorService } from "../../competitors/CompetitorService";
import { logger } from "../../config/logger";

// Initialize services
const cannMenusApiService = new CannMenusApiService({
  baseUrl: process.env.CANNMENUS_API_URL || "",
  apiKey: process.env.CANNMENUS_API_KEY || "",
});
const competitorService = new CompetitorService();

export const CannMenusRetailerSearchTool = tool(
  async ({ query, limit }: { query: string; limit?: number }) => {
    try {
      logger.info(
        "🔥 CannMenusRetailerSearchTool called with structured input",
        {
          query,
          limit,
          inputTypes: { query: typeof query, limit: typeof limit },
        }
      );

      if (!query) {
        logger.error("Invalid or missing query", { query });
        return JSON.stringify({
          error: "Query is required",
          details: "query must be provided",
        });
      }

      const finalLimit = limit || 10;
      const retailers = await cannMenusApiService.getRetailers({
        name: query,
      });

      logger.info("Retrieved retailers from CannMenus API successfully", {
        query,
        limit: finalLimit,
        count: retailers.length,
      });

      return JSON.stringify({ retailers, count: retailers.length });
    } catch (error) {
      logger.error("CannMenusRetailerSearchTool error", { error });
      return JSON.stringify({
        error: "Failed to search retailers",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "cannmenus_retailer_search",
    description:
      "Search for cannabis retailers using live CannMenus API by name, location, or other keywords. REQUIRES: query (string) - search keywords, limit (number, optional) - max results to return (default: 10)",
    schema: z.object({
      query: z.string().describe("Search keywords for finding retailers"),
      limit: z
        .number()
        .optional()
        .describe("Maximum number of results to return (default: 10)"),
    }),
  }
);

export const CannMenusNearbyRetailersTool = tool(
  async ({
    latitude,
    longitude,
    distance,
    limit,
  }: {
    latitude: number;
    longitude: number;
    distance?: number;
    limit?: number;
  }) => {
    try {
      logger.info(
        "🔥 CannMenusNearbyRetailersTool called with structured input",
        {
          latitude,
          longitude,
          distance,
          limit,
          inputTypes: {
            latitude: typeof latitude,
            longitude: typeof longitude,
            distance: typeof distance,
            limit: typeof limit,
          },
        }
      );

      if (!latitude || !longitude || isNaN(latitude) || isNaN(longitude)) {
        logger.error("Invalid or missing coordinates", { latitude, longitude });
        return JSON.stringify({
          error: "Valid latitude and longitude are required",
          details: "Both latitude and longitude must be valid numbers",
        });
      }

      const finalDistance = distance || 25;
      const finalLimit = limit || 10;

      const retailers = await cannMenusApiService.getRetailers({
        lat: Number(latitude),
        lng: Number(longitude),
        distance: finalDistance,
      });

      logger.info(
        "Retrieved nearby retailers from CannMenus API successfully",
        {
          latitude,
          longitude,
          distance: finalDistance,
          limit: finalLimit,
          count: retailers.length,
        }
      );

      return JSON.stringify({ retailers, count: retailers.length });
    } catch (error) {
      logger.error("CannMenusNearbyRetailersTool error", { error });
      return JSON.stringify({
        error: "Failed to find nearby retailers",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "cannmenus_nearby_retailers",
    description:
      "Find cannabis retailers near a specific location using live CannMenus API. REQUIRES: latitude (number), longitude (number), distance (number, optional) - search radius in miles (default: 25), limit (number, optional) - max results (default: 10)",
    schema: z.object({
      latitude: z
        .number()
        .describe("Latitude coordinate for the search center"),
      longitude: z
        .number()
        .describe("Longitude coordinate for the search center"),
      distance: z
        .number()
        .optional()
        .describe("Search radius in miles (default: 25)"),
      limit: z
        .number()
        .optional()
        .describe("Maximum number of results to return (default: 10)"),
    }),
  }
);

export const CannMenusRetailerProductsTool = tool(
  async ({ retailerId, limit }: { retailerId: string; limit?: number }) => {
    try {
      logger.info(
        "🔥 CannMenusRetailerProductsTool called with structured input",
        {
          retailerId,
          limit,
          inputTypes: { retailerId: typeof retailerId, limit: typeof limit },
        }
      );

      if (!retailerId) {
        logger.error("Invalid or missing retailerId", { retailerId });
        return JSON.stringify({
          error: "Retailer ID is required",
          details: "retailerId must be provided",
        });
      }

      const finalLimit = limit || 100;
      const products = await cannMenusApiService.getProducts({
        retailers: [parseInt(retailerId)],
        limit: finalLimit,
      });

      logger.info(
        "Retrieved retailer products from CannMenus API successfully",
        {
          retailerId,
          limit: finalLimit,
          count: products.length,
        }
      );

      return JSON.stringify({ products, count: products.length });
    } catch (error) {
      logger.error("CannMenusRetailerProductsTool error", { error });
      return JSON.stringify({
        error: "Failed to get retailer products",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "cannmenus_retailer_products",
    description:
      "Get products for a specific retailer using live CannMenus API. REQUIRES: retailerId (string) - the ID of the retailer to get products for, limit (number, optional) - max number of products to return (default: 100)",
    schema: z.object({
      retailerId: z
        .string()
        .describe("The ID of the retailer to get products for"),
      limit: z
        .number()
        .optional()
        .describe("Maximum number of products to return (default: 100)"),
    }),
  }
);

export const CannMenusProductSearchTool = tool(
  async ({
    query,
    category,
    brand,
    latitude,
    longitude,
    distance,
    limit,
  }: {
    query: string;
    category?: string;
    brand?: string;
    latitude?: number;
    longitude?: number;
    distance?: number;
    limit?: number;
  }) => {
    try {
      logger.info(
        "🔥 CannMenusProductSearchTool called with structured input",
        {
          query,
          category,
          brand,
          latitude,
          longitude,
          distance,
          limit,
          inputTypes: {
            query: typeof query,
            category: typeof category,
            brand: typeof brand,
            latitude: typeof latitude,
            longitude: typeof longitude,
            distance: typeof distance,
            limit: typeof limit,
          },
        }
      );

      if (!query) {
        logger.error("Invalid or missing query", { query });
        return JSON.stringify({
          error: "Query is required",
          details: "query must be provided for product search",
        });
      }

      const finalLimit = limit || 20;
      const finalDistance = distance || 25;

      const params: any = {
        search: query,
        limit: finalLimit,
      };

      if (category) params.category = category;
      if (brand) params.brand = brand;
      if (latitude && longitude) {
        params.lat = latitude;
        params.lng = longitude;
        params.distance = finalDistance;
      }

      const products = await cannMenusApiService.getProducts(params);

      logger.info("Product search completed successfully via CannMenus API", {
        query,
        category,
        brand,
        latitude,
        longitude,
        distance: finalDistance,
        limit: finalLimit,
        count: products.length,
      });

      return JSON.stringify({
        products,
        count: products.length,
      });
    } catch (error) {
      logger.error("CannMenusProductSearchTool error", { error });
      return JSON.stringify({
        error: "Failed to search products",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "cannmenus_product_search",
    description:
      "Search for cannabis products using live CannMenus API by name, category, or brand. REQUIRES: query (string) - search keywords, category (string, optional) - product category filter, brand (string, optional) - brand name filter, latitude/longitude (numbers, optional) - for location-based search, distance (number, optional) - search radius in miles (default: 25), limit (number, optional) - max results (default: 20)",
    schema: z.object({
      query: z.string().describe("Search keywords for finding products"),
      category: z.string().optional().describe("Product category to filter by"),
      brand: z.string().optional().describe("Brand name to filter by"),
      latitude: z
        .number()
        .optional()
        .describe("Latitude for location-based search"),
      longitude: z
        .number()
        .optional()
        .describe("Longitude for location-based search"),
      distance: z
        .number()
        .optional()
        .describe("Search radius in miles (default: 25)"),
      limit: z
        .number()
        .optional()
        .describe("Maximum number of results to return (default: 20)"),
    }),
  }
);

export const CannMenusMarketAnalysisTool = tool(
  async ({
    competitors,
    userRetailerId,
    latitude,
    longitude,
    distance,
  }: {
    competitors?: Array<{ place_id: string; name: string }>;
    userRetailerId?: string;
    latitude?: number;
    longitude?: number;
    distance?: number;
  }) => {
    try {
      logger.info(
        "🔥 CannMenusMarketAnalysisTool called with structured input",
        {
          competitors,
          userRetailerId,
          latitude,
          longitude,
          distance,
          inputTypes: {
            competitors: typeof competitors,
            userRetailerId: typeof userRetailerId,
            latitude: typeof latitude,
            longitude: typeof longitude,
            distance: typeof distance,
          },
          competitorsCount: Array.isArray(competitors) ? competitors.length : 0,
        }
      );

      // Use live market analysis from CannMenus API
      let analysis;

      if (competitors && Array.isArray(competitors) && competitors.length > 0) {
        // Use provided competitors list
        const retailerIds = competitors
          .map((comp) => parseInt(comp.place_id))
          .filter((id) => !isNaN(id));

        if (retailerIds.length > 0) {
          analysis = await cannMenusApiService.getBatchCompetitorData(
            retailerIds
          );
        } else {
          throw new Error("No valid retailer IDs found in competitors list");
        }
      } else if (latitude && longitude) {
        // Get market analysis by geographic area
        const finalDistance = distance || 25;
        analysis = await cannMenusApiService.getMarketAnalysis(
          latitude,
          longitude,
          {
            radius: finalDistance,
          }
        );
      } else {
        throw new Error(
          "Either competitors array or latitude/longitude coordinates are required"
        );
      }

      logger.info("Market analysis completed successfully via CannMenus API", {
        competitorsCount: competitors?.length || 0,
        hasAnalysis: !!analysis,
        analysisType: competitors?.length ? "competitor_based" : "geographic",
      });

      return JSON.stringify(analysis);
    } catch (error) {
      logger.error("CannMenusMarketAnalysisTool error", { error });
      return JSON.stringify({
        error: "Failed to perform market analysis",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "cannmenus_market_analysis",
    description:
      "Perform comprehensive market analysis using live CannMenus API data. Can analyze by competitor list OR geographic area. OPTIONAL: competitors (array) - array of competitor objects, userRetailerId (string) - user's retailer ID for comparison, latitude/longitude (numbers) - for geographic analysis, distance (number) - radius in miles (default: 25)",
    schema: z.object({
      competitors: z
        .array(
          z.object({
            name: z.string().describe("Competitor name"),
            place_id: z.string().describe("Competitor place ID"),
          })
        )
        .optional()
        .describe("Array of competitor objects to analyze"),
      userRetailerId: z
        .string()
        .optional()
        .describe("The user's retailer ID for comparison (optional)"),
      latitude: z
        .number()
        .optional()
        .describe("Latitude for geographic analysis"),
      longitude: z
        .number()
        .optional()
        .describe("Longitude for geographic analysis"),
      distance: z
        .number()
        .optional()
        .describe("Search radius in miles (default: 25)"),
    }),
  }
);

export const CannMenusCompetitorsTool = tool(
  async ({ locationId }: { locationId: number }) => {
    try {
      logger.info("🔥 CannMenusCompetitorsTool called with structured input", {
        locationId,
        inputType: typeof locationId,
      });

      if (!locationId || isNaN(Number(locationId))) {
        logger.error("Invalid or missing locationId", { locationId });
        return JSON.stringify({
          error: "Invalid locationId provided",
          details: "locationId must be a valid number",
        });
      }

      logger.info("Getting competitors for location using live market data", {
        locationId,
      });

      // Get competitors using the public competitor service method
      const competitors = await competitorService.getCompetitors(
        Number(locationId)
      );

      logger.info("Retrieved competitors successfully via live API", {
        locationId,
        count: competitors.length,
      });

      return JSON.stringify({
        count: competitors.length,
        locationId,
        competitors: competitors.map((c) => ({
          name: c.name,
          retailer_id: c.place_id,
          place_id: c.place_id,
          address: c.address,
          distance: c.distance,
          data_source: "live_api",
        })),
      });
    } catch (error) {
      logger.error("CannMenusCompetitorsTool error", { error });
      return JSON.stringify({
        error: "Failed to get competitors",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "cannmenus_get_competitors",
    description:
      "Get all competitors for a specific location using live CannMenus API data. REQUIRES: locationId (number) - the ID of the location to get competitors for.",
    schema: z.object({
      locationId: z
        .number()
        .describe("The ID of the location to get competitors for"),
    }),
  }
);

export const CannMenusBrandSearchTool = tool(
  async ({ brandName, limit }: { brandName: string; limit?: number }) => {
    try {
      logger.info("🔥 CannMenusBrandSearchTool called with structured input", {
        brandName,
        limit,
        inputTypes: { brandName: typeof brandName, limit: typeof limit },
      });

      if (!brandName) {
        logger.error("Invalid or missing brandName", { brandName });
        return JSON.stringify({
          error: "Brand name is required",
          details: "brandName must be provided",
        });
      }

      const finalLimit = limit || 10;
      const brands = await cannMenusApiService.getBrands({
        name: brandName,
      });

      logger.info("Brand search completed successfully via CannMenus API", {
        brandName,
        limit: finalLimit,
        count: brands.length,
      });

      return JSON.stringify({ brands, count: brands.length });
    } catch (error) {
      logger.error("CannMenusBrandSearchTool error", { error });
      return JSON.stringify({
        error: "Failed to search brands",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "cannmenus_brand_search",
    description:
      "Search for cannabis brands using live CannMenus API by name or other criteria. REQUIRES: brandName (string) - name of the brand to search for, limit (number, optional) - max results to return (default: 10)",
    schema: z.object({
      brandName: z.string().describe("Name of the brand to search for"),
      limit: z
        .number()
        .optional()
        .describe("Maximum number of results to return (default: 10)"),
    }),
  }
);

export const CannMenusProductAnalysisTool = tool(
  async ({
    category,
    brand,
    latitude,
    longitude,
    distance,
  }: {
    category?: string;
    brand?: string;
    latitude?: number;
    longitude?: number;
    distance?: number;
  }) => {
    try {
      logger.info(
        "🔥 CannMenusProductAnalysisTool called with structured input",
        {
          category,
          brand,
          latitude,
          longitude,
          distance,
          inputTypes: {
            category: typeof category,
            brand: typeof brand,
            latitude: typeof latitude,
            longitude: typeof longitude,
            distance: typeof distance,
          },
        }
      );

      const finalDistance = distance || 25;
      const params: any = { limit: 500 };

      if (category) params.category = category;
      if (brand) params.brand = brand;
      if (latitude && longitude) {
        params.lat = latitude;
        params.lng = longitude;
        params.distance = finalDistance;
      }

      const products = await cannMenusApiService.getProducts(params);

      // Group by category and calculate average prices
      const productsByCategory: Record<string, any[]> = {};
      products.forEach((product: any) => {
        const productCategory = product.category || "Unknown";
        if (!productsByCategory[productCategory]) {
          productsByCategory[productCategory] = [];
        }
        productsByCategory[productCategory].push(product);
      });

      // Calculate statistics
      const analysis = Object.entries(productsByCategory).map(
        ([category, products]) => {
          const prices = products
            .map((p) => p.price)
            .filter(
              (price) => price !== null && price !== undefined && price > 0
            );

          const avgPrice =
            prices.length > 0
              ? prices.reduce((sum, price) => sum + price, 0) / prices.length
              : 0;

          return {
            category,
            product_count: products.length,
            average_price: avgPrice.toFixed(2),
            price_range:
              prices.length > 0
                ? {
                    min: Math.min(...prices).toFixed(2),
                    max: Math.max(...prices).toFixed(2),
                  }
                : null,
          };
        }
      );

      logger.info("Product analysis completed successfully via CannMenus API", {
        category,
        brand,
        latitude,
        longitude,
        distance: finalDistance,
        totalProducts: products.length,
        categoriesAnalyzed: Object.keys(productsByCategory).length,
      });

      return JSON.stringify({
        analysis,
        total_products: products.length,
        categories_count: Object.keys(productsByCategory).length,
        data_source: "live_cannmenus_api",
      });
    } catch (error) {
      logger.error("CannMenusProductAnalysisTool error", { error });
      return JSON.stringify({
        error: "Failed to analyze products",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "cannmenus_product_analysis",
    description:
      "Analyze product pricing, categories, and availability using live CannMenus API data. OPTIONAL: category (string) - specific category to analyze, brand (string) - specific brand to analyze, latitude/longitude (numbers) - for location-based analysis, distance (number) - radius in miles (default: 25)",
    schema: z.object({
      category: z
        .string()
        .optional()
        .describe("Product category to analyze (optional)"),
      brand: z.string().optional().describe("Brand name to analyze (optional)"),
      latitude: z
        .number()
        .optional()
        .describe("Latitude for location-based analysis"),
      longitude: z
        .number()
        .optional()
        .describe("Longitude for location-based analysis"),
      distance: z
        .number()
        .optional()
        .describe("Search radius in miles (default: 25)"),
    }),
  }
);

export const CannMenusRetailerDetailsTool = tool(
  async ({ retailerId }: { retailerId: string }) => {
    try {
      logger.info(
        "🔥 CannMenusRetailerDetailsTool called with structured input",
        {
          retailerId,
          inputType: typeof retailerId,
        }
      );

      if (!retailerId) {
        logger.error("Invalid or missing retailerId", { retailerId });
        return JSON.stringify({
          error: "Retailer ID is required",
          details: "retailerId must be provided",
        });
      }

      // Convert to number if it's a string number
      const numericRetailerId = parseInt(retailerId);
      if (isNaN(numericRetailerId)) {
        logger.error("Invalid retailerId format", { retailerId });
        return JSON.stringify({
          error: "Invalid retailer ID format",
          details: "retailerId must be a valid number",
        });
      }

      // Get retailer details from CannMenus API
      const retailer = await cannMenusApiService.getRetailers({
        id: numericRetailerId,
      });

      if (!retailer || !retailer.data || retailer.data.length === 0) {
        logger.warn("No retailer found", { retailerId });
        return JSON.stringify({
          error: "Retailer not found",
          details: `No retailer found with ID: ${retailerId}`,
        });
      }

      const retailerData = retailer.data[0]; // Get first result

      logger.info("Retrieved retailer details successfully", {
        retailerId,
        retailerName: retailerData.name,
        hasData: !!retailerData,
      });

      return JSON.stringify({
        retailer: retailerData,
        source: "cannmenus_api",
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error("CannMenusRetailerDetailsTool error", { error });
      return JSON.stringify({
        error: "Failed to get retailer details",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "cannmenus_retailer_details",
    description:
      "Get detailed information about a specific retailer using live CannMenus API data. Provides current retailer information including location, contact details, and operational status.",
    schema: z.object({
      retailerId: z
        .string()
        .describe("The ID of the retailer to get details for"),
    }),
  }
);

export const CannMenusRegionalMarketAnalysisByLocationTool = tool(
  async ({
    city,
    state,
    userRetailerId,
    maxRetailers,
  }: {
    city?: string;
    state?: string;
    userRetailerId?: string;
    maxRetailers?: number;
  }) => {
    try {
      logger.info("🔥 CannMenusRegionalMarketAnalysisByLocationTool called", {
        city,
        state,
        userRetailerId,
        maxRetailers,
      });

      if (!city && !state) {
        return JSON.stringify({
          error: "City or state is required",
          details: "Either city or state must be provided",
        });
      }

      const finalMaxRetailers = maxRetailers || 50;

      // First, get retailers in the specified city/state
      const retailersQuery: any = {};
      if (city) retailersQuery.city = city;
      if (state) retailersQuery.state = state;

      const retailersResponse = await cannMenusApiService.getRetailers(
        retailersQuery
      );

      const retailers = retailersResponse.data || [];

      if (retailers.length === 0) {
        return JSON.stringify({
          analysisType: "regional-location",
          location: { city, state },
          competitorCount: 0,
          hotCategories: [],
          gaps: ["No retailers found in the specified location."],
          insights: ["Try expanding the search to include neighboring areas."],
          recommendations: [],
          regionStats: {
            totalRetailers: 0,
            totalProducts: 0,
            citiesAnalyzed: [],
            topRetailers: [],
          },
        });
      }

      // Get market data for these retailers
      const retailerIds = retailers
        .slice(0, finalMaxRetailers)
        .map((r: any) => r.id);

      const marketData = await cannMenusApiService.getBatchCompetitorData(
        retailerIds,
        {
          categories: ["Flower", "Edibles", "Vapes", "Concentrates"],
          limit: 200,
        }
      );

      // Transform the data for regional analysis
      const analysis = {
        analysisType: "regional-location",
        location: { city, state },
        competitorCount: retailers.length,
        hotCategories: [],
        gaps: [],
        insights: [
          `Analyzed ${retailers.length} retailers in the region.`,
          `Found ${marketData.categories?.length || 0} product categories.`,
        ],
        recommendations: [
          "Regional analysis provides broader market context for pricing decisions.",
          "Consider the market positioning across different retailers.",
        ],
        regionStats: {
          totalRetailers: retailers.length,
          totalProducts:
            marketData.categories?.reduce(
              (sum: number, cat: any) => sum + (cat.data?.data?.length || 0),
              0
            ) || 0,
          citiesAnalyzed: [...new Set(retailers.map((r: any) => r.city))],
          topRetailers: retailers.slice(0, 5).map((r: any) => ({
            retailerId: r.id,
            name: r.name,
            city: r.city,
          })),
        },
        generatedAt: new Date().toISOString(),
        totalProductsAnalyzed:
          marketData.categories?.reduce(
            (sum: number, cat: any) => sum + (cat.data?.data?.length || 0),
            0
          ) || 0,
        categoriesFound: marketData.categories?.length || 0,
        dataSource: "cannmenus_live_api",
      };

      return JSON.stringify(analysis);
    } catch (error) {
      logger.error("CannMenusRegionalMarketAnalysisByLocationTool error", {
        error,
      });
      return JSON.stringify({
        error: "Failed to perform regional market analysis",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "CannMenusRegionalMarketAnalysisByLocationTool",
    description:
      "Analyze regional market data across a city, state, or broader geographic area using live CannMenus API data. Provides insights into regional pricing trends, competitor density, market opportunities, and geographic market positioning.",
    schema: z.object({
      city: z
        .string()
        .optional()
        .describe(
          "City name for regional analysis (optional if state is provided)"
        ),
      state: z
        .string()
        .optional()
        .describe(
          "State name for regional analysis (optional if city is provided)"
        ),
      userRetailerId: z
        .string()
        .optional()
        .describe(
          "Optional retailer ID of the user's business for comparison in regional context"
        ),
      maxRetailers: z
        .number()
        .min(1)
        .max(100)
        .optional()
        .describe(
          "Maximum number of retailers to include in analysis (1-100, default 50)"
        ),
    }),
  }
);

export const CannMenusRegionalMarketAnalysisByRadiusTool = tool(
  async ({
    latitude,
    longitude,
    radiusMiles,
    userRetailerId,
    maxRetailers,
  }: {
    latitude: number;
    longitude: number;
    radiusMiles: number;
    userRetailerId?: string;
    maxRetailers?: number;
  }) => {
    try {
      logger.info("🔥 CannMenusRegionalMarketAnalysisByRadiusTool called", {
        latitude,
        longitude,
        radiusMiles,
        userRetailerId,
        maxRetailers,
      });

      if (!latitude || !longitude || isNaN(latitude) || isNaN(longitude)) {
        return JSON.stringify({
          error: "Valid latitude and longitude are required",
          details: "Both latitude and longitude must be valid numbers",
        });
      }

      const finalRadiusMiles = radiusMiles || 30;
      const finalMaxRetailers = maxRetailers || 50;

      // Use the market analysis method which handles radius-based searches
      const marketData = await cannMenusApiService.getMarketAnalysis(
        latitude,
        longitude,
        {
          radius: finalRadiusMiles,
          categories: ["Flower", "Edibles", "Vapes", "Concentrates"],
          limit: 200,
        }
      );

      if (!marketData.retailers || marketData.retailers.length === 0) {
        return JSON.stringify({
          analysisType: "regional-radius",
          location: {
            latitude,
            longitude,
            radiusMiles: finalRadiusMiles,
          },
          competitorCount: 0,
          hotCategories: [],
          gaps: [
            `No retailers found within ${finalRadiusMiles} miles of the specified location.`,
          ],
          insights: [
            "Try increasing the radius or checking if there are retailers in the area.",
          ],
          recommendations: [],
          regionStats: {
            totalRetailers: 0,
            totalProducts: 0,
            averageDistance: 0,
            closestRetailer: null,
            farthestRetailer: null,
          },
        });
      }

      // Calculate distance statistics
      const retailers = marketData.retailers.slice(0, finalMaxRetailers);
      const distances = retailers
        .map((r: any) => r.distance)
        .filter((d: any) => d !== undefined);

      const averageDistance =
        distances.length > 0
          ? distances.reduce((sum: number, d: number) => sum + d, 0) /
            distances.length
          : 0;

      const closestRetailer = retailers.reduce((closest: any, current: any) => {
        return current.distance < (closest?.distance || Infinity)
          ? current
          : closest;
      }, null);

      const farthestRetailer = retailers.reduce(
        (farthest: any, current: any) => {
          return current.distance > (farthest?.distance || 0)
            ? current
            : farthest;
        },
        null
      );

      const analysis = {
        analysisType: "regional-radius",
        location: {
          latitude,
          longitude,
          radiusMiles: finalRadiusMiles,
        },
        competitorCount: retailers.length,
        hotCategories: [],
        gaps: [],
        insights: [
          `Analyzed ${retailers.length} retailers within ${finalRadiusMiles} miles.`,
          `Average distance to retailers: ${averageDistance.toFixed(1)} miles.`,
          closestRetailer
            ? `Closest retailer: ${
                closestRetailer.name
              } at ${closestRetailer.distance.toFixed(1)} miles.`
            : "No distance data available.",
        ],
        recommendations: [
          "Radius-based analysis helps understand your immediate competitive landscape.",
          "Consider both distance and pricing when setting competitive strategies.",
          averageDistance > finalRadiusMiles * 0.7
            ? "Most retailers are far from your location - you may have geographic pricing flexibility."
            : "High retailer density nearby - focus on differentiation and competitive pricing.",
        ],
        regionStats: {
          totalRetailers: retailers.length,
          totalProducts:
            marketData.categories?.reduce(
              (sum: number, cat: any) => sum + (cat.data?.data?.length || 0),
              0
            ) || 0,
          averageDistance: parseFloat(averageDistance.toFixed(1)),
          closestRetailer: closestRetailer
            ? {
                name: closestRetailer.name,
                distance: parseFloat(closestRetailer.distance.toFixed(1)),
                city: closestRetailer.city,
              }
            : null,
          farthestRetailer: farthestRetailer
            ? {
                name: farthestRetailer.name,
                distance: parseFloat(farthestRetailer.distance.toFixed(1)),
                city: farthestRetailer.city,
              }
            : null,
        },
        generatedAt: new Date().toISOString(),
        totalProductsAnalyzed:
          marketData.categories?.reduce(
            (sum: number, cat: any) => sum + (cat.data?.data?.length || 0),
            0
          ) || 0,
        categoriesFound: marketData.categories?.length || 0,
        dataSource: "cannmenus_live_api",
      };

      return JSON.stringify(analysis);
    } catch (error) {
      logger.error("CannMenusRegionalMarketAnalysisByRadiusTool error", {
        error,
      });
      return JSON.stringify({
        error: "Failed to perform regional market analysis",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "CannMenusRegionalMarketAnalysisByRadiusTool",
    description:
      "Analyze market data within a specified radius from geographic coordinates using live CannMenus API data. Provides distance-based insights, competitor proximity analysis, geographic density patterns, and location-specific market opportunities.",
    schema: z.object({
      latitude: z
        .number()
        .describe("Center latitude for radius-based analysis")
        .min(-90)
        .max(90),
      longitude: z
        .number()
        .describe("Center longitude for radius-based analysis")
        .min(-180)
        .max(180),
      radiusMiles: z
        .number()
        .describe("Radius in miles for analysis (1-500, default 30)")
        .min(1)
        .max(500),
      userRetailerId: z
        .string()
        .optional()
        .describe(
          "Optional retailer ID of the user's business for comparison in regional context"
        ),
      maxRetailers: z
        .number()
        .min(1)
        .max(100)
        .optional()
        .describe(
          "Maximum number of retailers to include in analysis (1-100, default 50)"
        ),
    }),
  }
);
