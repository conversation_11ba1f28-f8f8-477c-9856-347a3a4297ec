import Model from "../core/Model";

export class Inventory extends Model {
  location_id!: number;
  product_id?: string;
  pos_product_id?: string;
  crb_id?: string;

  // Inventory Quantities
  quantity_on_hand!: number;
  quantity_available!: number;
  quantity_reserved!: number;
  quantity_committed!: number;

  // Weight-based tracking (for cannabis products)
  grams_on_hand!: number;
  grams_available!: number;

  // Pricing Information
  unit_cost?: number;
  unit_price?: number;
  wholesale_price?: number;
  retail_price?: number;

  // POS Integration Fields
  pos_type?: string;
  inventory_location_id?: string;
  inventory_location_name?: string;

  // Product Information (cached from products table)
  product_name?: string;
  product_sku?: string;
  product_category?: string;
  product_subcategory?: string;
  brand_name?: string;
  strain_name?: string;

  // Cannabis-Specific Fields
  is_cannabis_product!: boolean;
  thc_percentage?: number;
  cbd_percentage?: number;
  batch_number?: string;
  harvest_date?: Date;
  expiration_date?: Date;

  // Status and Tracking
  status!: string;
  is_trackable!: boolean;
  last_counted_at?: Date;
  last_movement_at?: Date;

  // POS Timestamps
  pos_updated_date?: Date;
  pos_updated_date_local?: Date;

  static tableName = "inventory";
  static jsonAttributes = [];

  // Virtual attributes
  static get virtualAttributes() {
    return ["isLowStock", "isOutOfStock", "totalValue"];
  }

  get isLowStock(): boolean {
    // Consider low stock if available quantity is less than 10% of on-hand
    const threshold = this.quantity_on_hand * 0.1;
    return this.quantity_available <= threshold;
  }

  get isOutOfStock(): boolean {
    return this.quantity_available <= 0;
  }

  get totalValue(): number {
    const price = this.unit_price || this.wholesale_price || 0;
    return this.quantity_on_hand * price;
  }

  // Static methods for common queries
  static findByLocation(locationId: number) {
    return this.query().where("location_id", locationId);
  }

  static findByProduct(locationId: number, productId: string) {
    return this.query()
      .where("location_id", locationId)
      .where("product_id", productId);
  }

  static findByPosProduct(locationId: number, posProductId: string) {
    return this.query()
      .where("location_id", locationId)
      .where("pos_product_id", posProductId);
  }

  static findLowStock(locationId: number) {
    return this.query()
      .where("location_id", locationId)
      .whereRaw("quantity_available <= quantity_on_hand * 0.1")
      .where("status", "active");
  }

  static findOutOfStock(locationId: number) {
    return this.query()
      .where("location_id", locationId)
      .where("quantity_available", "<=", 0)
      .where("status", "active");
  }

  static findCannabisProducts(locationId: number) {
    return this.query()
      .where("location_id", locationId)
      .where("is_cannabis_product", true);
  }
}
