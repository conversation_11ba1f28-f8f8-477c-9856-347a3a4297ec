/**
 * @swagger
 * tags:
 *   name: Misc
 *   description: Miscellaneous endpoints for various functionalities
 */

import Router from "@koa/router";
import axios from "axios";
import qs from "qs";
import { validateBody } from "./MiscMiddleware";
import {
  completeOnboardingRequestSchema,
  searchNearbyRequestSchema,
  searchPlacesRequestSchema,
  userInfoRequestSchema,
  geocodeRequestSchema,
  linkedInTokenRequestSchema,
  subscriptionUpgradeRequestSchema,
  searchEventsRequestSchema,
} from "../core/schemas";
import Stripe from "stripe";
import {
  getStripeSubscriptionById,
  canCreateLocation,
} from "../subscriptions/SubscriptionService";
import StripeSubscription, {
  SubscriptionStatus,
} from "../subscriptions/StripeSubscription";
import { SupabaseService } from "../supabase/SupabaseService";
import { Context } from "koa";
import { logger } from "../config/logger";
import Model from "../core/Model";
import { competitorService } from "../competitors/CompetitorService";
import { validateInviteCode } from "../organizations/OrganizationService";

// Add type definition for Koa request with rawBody
declare module "koa" {
  interface Request {
    rawBody: string;
  }
}

// Add model for tracking processed Stripe events
class ProcessedStripeEvent extends Model {
  static tableName = "processed_stripe_events";

  declare id: number;
  event_id!: string;
  event_type!: string;
  processed_at!: Date;

  static async isEventProcessed(eventId: string): Promise<boolean> {
    const event = await ProcessedStripeEvent.first((qb) =>
      qb.where({ event_id: eventId })
    );
    return !!event;
  }

  static async markEventAsProcessed(
    eventId: string,
    eventType: string
  ): Promise<void> {
    await ProcessedStripeEvent.insert({
      event_id: eventId,
      event_type: eventType,
      processed_at: new Date(),
    });
  }
}

const router = new Router({
  prefix: "/misc",
});

// Helper function to get Supabase service
const getSupabaseService = () => {
  return new SupabaseService({
    url: process.env.SUPABASE_URL || "",
    key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
    bucket: process.env.SUPABASE_BUCKET || "location-data",
  });
};

/**
 * @swagger
 * /misc/retailers/search:
 *   get:
 *     summary: Search Retailers
 *     description: Search for retailers based on query parameters
 *     tags: [Misc]
 *     parameters:
 *       - in: query
 *         name: query
 *         schema:
 *           type: string
 *         description: Search query string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Maximum number of results to return
 *     responses:
 *       200:
 *         description: List of matching retailers
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   address:
 *                     type: string
 */
router.get("/retailers/search", async (ctx: Context) => {
  const { query } = ctx.query;

  if (!query || typeof query !== "string") {
    ctx.status = 400;
    ctx.body = {
      error: "Query parameter is required",
    };
    return;
  }

  try {
    // This endpoint is used for competitor search - database only, no Google Places fallback
    const retailers = await competitorService.searchRetailers(query, 10);

    ctx.status = 200;
    ctx.body = { retailers };
  } catch (error) {
    logger.error("Error searching retailers for competitors:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to search retailers",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /misc/retailers/business-search:
 *   get:
 *     summary: Search for business retailers with enhanced logic and Google Places fallback
 *     description: Enhanced business search with word count logic and Google Places fallback
 *     tags: [Misc]
 *     parameters:
 *       - in: query
 *         name: query
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query for business retailers
 *     responses:
 *       200:
 *         description: List of business retailers with enhanced search logic
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 retailers:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       address:
 *                         type: string
 */
router.get("/retailers/business-search", async (ctx: Context) => {
  const { query } = ctx.query;

  if (!query || typeof query !== "string") {
    ctx.status = 400;
    ctx.body = {
      error: "Query parameter is required",
    };
    return;
  }

  try {
    // This endpoint is used for business search with enhanced logic and Google Places fallback
    const retailers = await competitorService.searchBusinessRetailers(query, 10);

    ctx.status = 200;
    ctx.body = { retailers };
  } catch (error) {
    logger.error("Error searching business retailers:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to search business retailers",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

// Enhanced business search by location only (city/state) with Google Places fallback
router.get("/retailers/business-search-location", async (ctx: Context) => {
  const { query } = ctx.query;

  if (!query) {
    ctx.status = 400;
    ctx.body = {
      error: "Search query is required",
    };
    return;
  }

  try {
    // This endpoint is used for business search by location with enhanced logic and Google Places fallback
    const retailers = await competitorService.searchBusinessRetailersByLocation(query as string, 10);

    ctx.status = 200;
    ctx.body = { retailers };
  } catch (error) {
    logger.error("Error searching business retailers by location:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to search business retailers by location",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

// Competitor search by location only (city/state) - database only, no Google Places fallback
router.get("/retailers/search-location", async (ctx: Context) => {
  const { query } = ctx.query;

  if (!query) {
    ctx.status = 400;
    ctx.body = {
      error: "Search query is required",
    };
    return;
  }

  try {
    // This endpoint is used for competitor search by location - database only, no Google Places fallback
    const retailers = await competitorService.searchCompetitorRetailersByLocation(query as string, 10);

    ctx.status = 200;
    ctx.body = { retailers };
  } catch (error) {
    logger.error("Error searching competitor retailers by location:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to search competitor retailers by location",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /misc/retailers/nearby:
 *   get:
 *     summary: Find Nearby Retailers
 *     description: Find retailers near a specific location
 *     tags: [Misc]
 *     parameters:
 *       - in: query
 *         name: lat
 *         required: true
 *         schema:
 *           type: number
 *         description: Latitude coordinate
 *       - in: query
 *         name: lng
 *         required: true
 *         schema:
 *           type: number
 *         description: Longitude coordinate
 *       - in: query
 *         name: radius
 *         schema:
 *           type: number
 *         description: Search radius in kilometers
 *     responses:
 *       200:
 *         description: List of nearby retailers
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   distance:
 *                     type: number
 */
router.get("/retailers/nearby", async (ctx: Context) => {
  const {
    latitude,
    longitude,
    radius = "30",
    page = "1",
    pageSize = "4",
  } = ctx.query;

  if (!latitude || !longitude) {
    ctx.status = 400;
    ctx.body = {
      error: "Latitude and longitude parameters are required",
    };
    return;
  }

  try {
    const lat = parseFloat(latitude as string);
    const lng = parseFloat(longitude as string);
    const radiusMiles = parseFloat(radius as string);
    const currentPage = parseInt(page as string, 10);
    const limit = parseInt(pageSize as string, 10);

    if (
      isNaN(lat) ||
      isNaN(lng) ||
      isNaN(radiusMiles) ||
      isNaN(currentPage) ||
      isNaN(limit)
    ) {
      ctx.status = 400;
      ctx.body = {
        error: "Invalid parameters provided",
      };
      return;
    }

    const result = await competitorService.searchNearbyRetailers(
      lat,
      lng,
      radiusMiles,
      limit * 2,
      currentPage,
      limit
    );

    ctx.status = 200;
    ctx.body = result;
  } catch (error) {
    logger.error("Error searching nearby retailers:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to search nearby retailers",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /misc/retailers/{retailerId}:
 *   get:
 *     summary: Get Retailer Details
 *     description: Get detailed information about a specific retailer
 *     tags: [Misc]
 *     parameters:
 *       - in: path
 *         name: retailerId
 *         required: true
 *         schema:
 *           type: string
 *         description: Retailer identifier
 *     responses:
 *       200:
 *         description: Retailer details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 name:
 *                   type: string
 *                 address:
 *                   type: string
 *                 contact:
 *                   type: object
 *       404:
 *         description: Retailer not found
 */
router.get("/retailers/:retailerId", async (ctx: Context) => {
  const { retailerId } = ctx.params;

  if (!retailerId) {
    ctx.status = 400;
    ctx.body = {
      error: "Retailer ID is required",
    };
    return;
  }

  try {
    const retailer = await competitorService.getRetailerById(retailerId);

    if (!retailer) {
      ctx.status = 404;
      ctx.body = {
        error: "Retailer not found",
      };
      return;
    }

    ctx.status = 200;
    ctx.body = { retailer };
  } catch (error) {
    logger.error("Error getting retailer:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to get retailer",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /misc/retailers/{retailerId}/products:
 *   get:
 *     summary: Get Retailer Products
 *     description: Get list of products available at a specific retailer
 *     tags: [Misc]
 *     parameters:
 *       - in: path
 *         name: retailerId
 *         required: true
 *         schema:
 *           type: string
 *         description: Retailer identifier
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of products
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 products:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 */
router.get("/retailers/:retailerId/products", async (ctx: Context) => {
  const { retailerId } = ctx.params;
  const { limit = "100" } = ctx.query;

  if (!retailerId) {
    ctx.status = 400;
    ctx.body = {
      error: "Retailer ID is required",
    };
    return;
  }

  try {
    const limitNum = parseInt(limit as string);
    const products = await competitorService.getRetailerProducts(
      retailerId,
      limitNum
    );

    ctx.status = 200;
    // Assuming the API returns either an array of products or an object with products and count
    if (Array.isArray(products)) {
      ctx.body = {
        products,
        total_count: products.length,
      };
    } else {
      ctx.body = products; // Pass through the response as is
    }
  } catch (error) {
    logger.error("Error getting retailer products:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to get retailer products",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /misc/subscriptions/can-create-location/{organizationId}:
 *   get:
 *     summary: Check Location Creation Permission
 *     description: Check if an organization can create a new location based on their subscription
 *     tags: [Misc]
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization identifier
 *     responses:
 *       200:
 *         description: Permission check result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 can_create:
 *                   type: boolean
 *                 reason:
 *                   type: string
 */
router.get(
  "/subscriptions/can-create-location/:organizationId",
  async (ctx) => {
    try {
      console.log(
        "Accessing subscription check endpoint with params:",
        ctx.params
      );
      const organizationId = parseInt(ctx.params.organizationId);
      if (isNaN(organizationId)) {
        ctx.status = 400;
        ctx.body = { message: "Invalid organization ID" };
        return;
      }

      const canCreate = await canCreateLocation(organizationId);
      console.log(
        `Organization ${organizationId} can create location: ${canCreate}`
      );

      ctx.status = 200;
      ctx.body = {
        canCreate,
        message: canCreate
          ? "Organization can create more locations"
          : "Subscription limit reached. Please purchase additional subscriptions to create more locations.",
      };
    } catch (error) {
      console.error("Error in subscription check:", error);
      ctx.status = 500;
      ctx.body = { error: "Failed to check location creation eligibility" };
    }
  }
);

/**
 * @swagger
 * /misc/invite-codes/validate:
 *   post:
 *     summary: Validate Invite Code
 *     description: Validate an invitation code for organization access
 *     tags: [Misc]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *     responses:
 *       200:
 *         description: Validation result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 valid:
 *                   type: boolean
 *                 organization:
 *                   type: object
 *       400:
 *         description: Invalid code
 */
router.post("/invite-codes/validate", async (ctx) => {
  const { code } = ctx.request.body;
  if (!code || typeof code !== "string") {
    ctx.throw(400, "Invalid code format");
  }

  ctx.body = await validateInviteCode(code);
});

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2020-08-27" as any,
});

const ADD_ON_PRICE_ID = process.env.STRIPE_ADD_ON_PRICE_ID as string;
const endpointSecret = process.env.STRIPE_ENDPOINT_SECRET as string;

// Helper functions for Stripe webhook events
async function handleCheckoutSessionCompleted(
  session: Stripe.Checkout.Session
) {
  try {
    if (!session.subscription) return;

    // First try to get organizationId from metadata
    let organizationId = session.metadata?.organizationId;
    if (organizationId === "11") {
      return;
    }

    // If not found in metadata, try the client_reference_id field
    if (!organizationId || organizationId === "0") {
      if (session.client_reference_id) {
        organizationId = session.client_reference_id;
        console.log(
          "Using client_reference_id as organizationId:",
          organizationId
        );
      }
    }

    // Validate that we have a valid organization ID
    if (!organizationId || organizationId === "0") {
      console.error("Invalid or missing organization ID in checkout session:", {
        metadata: session.metadata,
        client_reference_id: session.client_reference_id,
      });
      throw new Error(
        "Missing or invalid organization_id in session metadata or client_reference_id"
      );
    }

    const subscription = await stripe.subscriptions.retrieve(
      session.subscription as string
    );

    // Check if subscription already exists in our database to prevent duplicates
    const existingSubscription = await StripeSubscription.query()
      .where({ subscription_id: subscription.id })
      .first();

    if (existingSubscription) {
      console.log(
        `Subscription ${subscription.id} already exists for organization ${organizationId}. Skipping creation.`
      );
      return;
    }

    // Check if the organization already has an active subscription (to handle accidental double subscriptions)
    const existingOrgSubscription = await StripeSubscription.query()
      .where({
        organization_id: parseInt(organizationId),
        status: SubscriptionStatus.active,
      })
      .first();

    if (existingOrgSubscription) {
      console.log(
        `Organization ${organizationId} already has an active subscription (${existingOrgSubscription.subscription_id}). Adding new subscription ${subscription.id} anyway, but this may need manual review.`
      );
      // We continue with creation but log this situation for review
      // Alternatively, we could cancel the new subscription if needed
    }

    // Get the quantity from the subscription items (defaults to 1 if not specified)
    const quantity = subscription.items.data[0].quantity || 1;

    console.log(
      `Subscription ${subscription.id} created with quantity ${quantity} for organization ${organizationId}`
    );

    // Create subscription record with trial information if present
    await StripeSubscription.insertAndFetch({
      organization_id: parseInt(organizationId),
      subscription_id: subscription.id,
      customer_id: subscription.customer as string,
      price_id: subscription.items.data[0].price.id,
      product_id: subscription.items.data[0].price.product as string,
      status: subscription.status as SubscriptionStatus,
      current_period_start: new Date(subscription.current_period_start * 1000),
      current_period_end: new Date(subscription.current_period_end * 1000),
      trial_start: subscription.trial_start
        ? new Date(subscription.trial_start * 1000)
        : undefined,
      trial_end: subscription.trial_end
        ? new Date(subscription.trial_end * 1000)
        : undefined,
      quantity,
    });

    // Log trial information if present
    if (subscription.trial_end) {
      console.log(
        `Subscription ${subscription.id} includes a trial ending at ${new Date(
          subscription.trial_end * 1000
        )}`
      );
    }
  } catch (error) {
    console.error("Error in handleCheckoutSessionCompleted:", error);
    throw error;
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    // Check if subscription exists in our database
    const existingSubscription = await StripeSubscription.query()
      .where({ subscription_id: subscription.id })
      .first();

    if (!existingSubscription) {
      console.log(
        `Subscription ${subscription.id} not found in database. Cannot update.`
      );
      return;
    }

    // Get the quantity from the subscription items (defaults to 1 if not specified)
    const quantity = subscription.items.data[0].quantity || 1;

    // Update subscription with trial information if present
    await StripeSubscription.update(
      (qb) => qb.where({ subscription_id: subscription.id }),
      {
        status: subscription.status as SubscriptionStatus,
        current_period_start: new Date(
          subscription.current_period_start * 1000
        ),
        current_period_end: new Date(subscription.current_period_end * 1000),
        trial_start: subscription.trial_start
          ? new Date(subscription.trial_start * 1000)
          : undefined,
        trial_end: subscription.trial_end
          ? new Date(subscription.trial_end * 1000)
          : undefined,
        quantity,
      }
    );

    console.log(
      `Subscription ${subscription.id} updated with quantity ${quantity}`
    );

    // Log trial information if present
    if (subscription.trial_end) {
      console.log(
        `Updated subscription ${
          subscription.id
        } includes a trial ending at ${new Date(subscription.trial_end * 1000)}`
      );
    }
  } catch (error) {
    console.error("Error in handleSubscriptionUpdated:", error);
    throw error;
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  try {
    // Check if subscription exists in our database
    const existingSubscription = await StripeSubscription.query()
      .where({ subscription_id: subscription.id })
      .first();

    if (!existingSubscription) {
      console.log(
        `Subscription ${subscription.id} not found in database. Cannot mark as canceled.`
      );
      return;
    }

    await StripeSubscription.update(
      (qb) => qb.where({ subscription_id: subscription.id }),
      {
        status: SubscriptionStatus.canceled,
        canceled_at: new Date(),
      }
    );
  } catch (error) {
    console.error("Error in handleSubscriptionDeleted:", error);
    throw error;
  }
}

async function handleSubscriptionUpgrade(session: Stripe.Checkout.Session) {
  // Implement if needed based on your specific requirements
  try {
    if (!session.subscription) return;
    const subscription = await stripe.subscriptions.retrieve(
      session.subscription as string
    );

    // Update the subscription record
    await handleSubscriptionUpdated(subscription);
  } catch (error) {
    console.error("Error in handleSubscriptionUpgrade:", error);
    throw error;
  }
}

async function upgradeSubscriptionWithAddOns(
  subscriptionId: string,
  newBasePlanPriceId: string,
  newAddOnPriceId: string,
  additionalLocations: number
) {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const basePlanItem = subscription.items.data.find(
      (item: Stripe.SubscriptionItem) => item.price.id !== ADD_ON_PRICE_ID
    );
    if (!basePlanItem) {
      throw new Error("Base plan subscription item not found.");
    }

    const addOnItem = subscription.items.data.find(
      (item: Stripe.SubscriptionItem) => item.price.id === ADD_ON_PRICE_ID
    );
    const updatedItems: any[] = [];

    updatedItems.push({ id: basePlanItem.id, price: newBasePlanPriceId });

    if (addOnItem) {
      if (addOnItem.price.id !== newAddOnPriceId) {
        await stripe.subscriptionItems.del(addOnItem.id);
        updatedItems.push({
          price: newAddOnPriceId,
          quantity: additionalLocations,
        });
      } else {
        updatedItems.push({ id: addOnItem.id, quantity: additionalLocations });
      }
    } else {
      updatedItems.push({
        price: newAddOnPriceId,
        quantity: additionalLocations,
      });
    }

    const updatedSubscription = await stripe.subscriptions.update(
      subscriptionId,
      {
        items: updatedItems,
        proration_behavior: "create_prorations",
        payment_settings: {
          payment_method_options: {
            card: {
              request_three_d_secure: "automatic",
            },
          },
        },
      }
    );

    await StripeSubscription.update(
      (qb) => qb.where({ subscription_id: subscriptionId }),
      {
        price_id: newBasePlanPriceId,
      }
    );

    return updatedSubscription;
  } catch (error) {
    console.error("Error in upgradeSubscriptionWithAddOns:", error);
    throw error;
  }
}

/**
 * @swagger
 * /misc/webhook/stripe:
 *   post:
 *     summary: Stripe Webhook Handler
 *     description: Handle incoming webhooks from Stripe payment system
 *     tags: [Misc]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Webhook processed successfully
 *       400:
 *         description: Invalid webhook data
 */
router.post("/webhook/stripe", async (ctx) => {
  const sig = ctx.headers["stripe-signature"] as string;
  try {
    // Using the rawBody captured by the middleware for signature verification
    const event = stripe.webhooks.constructEvent(
      ctx.request.rawBody,
      sig,
      endpointSecret
    );

    // Extract the event ID to prevent duplicate processing
    const eventId = event.id;

    // Check if we've already processed this event
    const isProcessed = await ProcessedStripeEvent.isEventProcessed(eventId);
    if (isProcessed) {
      console.log(`Event ${eventId} has already been processed. Skipping.`);
      ctx.status = 200;
      ctx.body = { received: true, duplicate: true };
      return;
    }

    console.log(`Processing Stripe event: ${eventId} (${event.type})`);

    switch (event.type) {
      case "checkout.session.completed": {
        const session = event.data.object as Stripe.Checkout.Session;
        if (session.metadata?.action === "upgrade") {
          await handleSubscriptionUpgrade(session);
        } else {
          await handleCheckoutSessionCompleted(session);
        }
        break;
      }
      case "customer.subscription.updated": {
        await handleSubscriptionUpdated(
          event.data.object as Stripe.Subscription
        );
        break;
      }
      case "customer.subscription.deleted": {
        await handleSubscriptionDeleted(
          event.data.object as Stripe.Subscription
        );
        break;
      }
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    // Mark the event as processed
    await ProcessedStripeEvent.markEventAsProcessed(eventId, event.type);

    ctx.body = { received: true };
  } catch (err) {
    console.error("Webhook Error:", (err as Error).message);
    ctx.status = 400;
    ctx.body = { error: `Webhook Error: ${(err as Error).message}` };
  }
});

/**
 * @swagger
 * /misc/subscription/upgrade-with-addons:
 *   post:
 *     summary: Upgrade Subscription with Addons
 *     description: Upgrade an organization's subscription with additional addons
 *     tags: [Misc]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               organization_id:
 *                 type: string
 *               addons:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Subscription upgraded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 subscription_id:
 *                   type: string
 *       400:
 *         description: Invalid request
 */
router.post(
  "/subscription/upgrade-with-addons",
  validateBody(subscriptionUpgradeRequestSchema),
  async (ctx) => {
    const {
      subscriptionId,
      newBasePlanPriceId,
      newAddOnPriceId,
      additionalLocations,
      organizationId,
    } = ctx.request.body;

    try {
      // Verify the subscription belongs to the organization
      const existingSubscription = await getStripeSubscriptionById(
        organizationId
      );
      if (
        !existingSubscription ||
        existingSubscription.subscription_id !== subscriptionId
      ) {
        ctx.status = 403;
        ctx.body = { error: "Unauthorized access to subscription." };
        return;
      }

      const updatedSubscription = await upgradeSubscriptionWithAddOns(
        subscriptionId,
        newBasePlanPriceId,
        newAddOnPriceId,
        additionalLocations
      );

      ctx.body = updatedSubscription;
    } catch (error) {
      console.error("Error in upgrade-with-addons:", error);
      ctx.status = 400;
      ctx.body = { error: (error as Error).message };
    }
  }
);

/**
 * @swagger
 * /misc/oauth/linkedin:
 *   post:
 *     summary: LinkedIn OAuth
 *     description: Handle LinkedIn OAuth authentication
 *     tags: [Misc]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *     responses:
 *       200:
 *         description: OAuth successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 access_token:
 *                   type: string
 *       400:
 *         description: Invalid OAuth request
 */
router.post(
  "/oauth/linkedin",
  validateBody(linkedInTokenRequestSchema),
  async (ctx) => {
    const { code } = ctx.request.body;

    const data = {
      grant_type: "authorization_code",
      code,
      redirect_uri: process.env.OAUTH_LINKEDIN_REDIRECT_URI,
      client_id: process.env.OAUTH_LINKEDIN_CLIENT_ID,
      client_secret: process.env.OAUTH_LINKEDIN_CLIENT_SECRET,
    };

    try {
      const response = await axios.post(
        "https://www.linkedin.com/oauth/v2/accessToken",
        qs.stringify(data),
        {
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
        }
      );
      ctx.status = 200;
      ctx.body = response.data;
    } catch (error) {
      ctx.status = 500;
      ctx.body = { error: "Failed to retrieve access token" };
    }
  }
);

/**
 * @swagger
 * /misc/oauth/linkedin/userinfo:
 *   post:
 *     summary: Get LinkedIn User Info
 *     description: Retrieve user information from LinkedIn OAuth
 *     tags: [Misc]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               access_token:
 *                 type: string
 *     responses:
 *       200:
 *         description: User information retrieved
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 name:
 *                   type: string
 *                 email:
 *                   type: string
 *       400:
 *         description: Invalid token
 */
router.post(
  "/oauth/linkedin/userinfo",
  validateBody(userInfoRequestSchema),
  async (ctx) => {
    const { accessToken } = ctx.request.body;

    try {
      const response = await axios.get("https://api.linkedin.com/v2/userinfo", {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });
      ctx.status = 200;
      ctx.body = response.data;
    } catch (error) {
      ctx.status = 500;
      ctx.body = { error: "Failed to retrieve user info" };
    }
  }
);

/**
 * @swagger
 * /misc/geocode:
 *   post:
 *     summary: Geocode Address
 *     description: Convert address to geographic coordinates
 *     tags: [Misc]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               address:
 *                 type: string
 *     responses:
 *       200:
 *         description: Geocoding result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 lat:
 *                   type: number
 *                 lng:
 *                   type: number
 *       400:
 *         description: Invalid address
 */
router.post("/geocode", validateBody(geocodeRequestSchema), async (ctx) => {
  const { placeId } = ctx.request.body;

  try {
    const response = await axios.post(
      `https://maps.googleapis.com/maps/api/geocode/json?place_id=${placeId.trim()}&key=${
        process.env.GOOGLE_MAPS_API_KEY
      }`
    );
    ctx.status = 200;
    ctx.body = response.data;
  } catch (error) {
    ctx.status = 500;
    ctx.body = { error: "Failed to geocode" };
  }
});

/**
 * @swagger
 * /misc/places/search:
 *   post:
 *     summary: Search Places
 *     description: Search for places using Google Places API
 *     tags: [Misc]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               query:
 *                 type: string
 *     responses:
 *       200:
 *         description: List of matching places
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   place_id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   address:
 *                     type: string
 */
router.post(
  "/places/search",
  validateBody(searchPlacesRequestSchema),
  async (ctx) => {
    const { input } = ctx.request.body;

    try {
      const response = await axios.post(
        "https://places.googleapis.com/v1/places:autocomplete",
        {
          input: input.trim(),
        },
        {
          headers: {
            "X-Goog-Api-Key": process.env.GOOGLE_MAPS_API_KEY,
          },
        }
      );
      ctx.status = 200;
      ctx.body = response.data;
    } catch (error) {
      ctx.status = 500;
      ctx.body = { error: "Failed to fetch places" };
    }
  }
);

/**
 * @swagger
 * /misc/places/search-nearby:
 *   post:
 *     summary: Search Nearby Places
 *     description: Search for places near a specific location
 *     tags: [Misc]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lat:
 *                 type: number
 *               lng:
 *                 type: number
 *               radius:
 *                 type: number
 *     responses:
 *       200:
 *         description: List of nearby places
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   place_id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   distance:
 *                     type: number
 */
router.post(
  "/places/search-nearby",
  validateBody(searchNearbyRequestSchema),
  async (ctx) => {
    const { longitude, latitude } = ctx.request.body;

    try {
      const response = await axios.post(
        "https://places.googleapis.com/v1/places:searchNearby",
        {
          includedTypes: ["corporate_office"],
          maxResultCount: 5,
          locationRestriction: {
            circle: {
              center: {
                latitude,
                longitude,
              },
              radius: 500.0,
            },
          },
        },
        {
          headers: {
            "X-Goog-Api-Key": process.env.GOOGLE_MAPS_API_KEY,
            "X-Goog-FieldMask":
              "places.displayName,places.id,places.location,places.addressComponents,places.formattedAddress",
          },
        }
      );
      ctx.status = 200;
      ctx.body = response.data;
    } catch (err) {
      console.log(err);
      ctx.status = 500;
      ctx.body = { error: "Failed to fetch nearby places" };
    }
  }
);

/**
 * @swagger
 * /misc/complete-onboarding:
 *   post:
 *     summary: Complete Onboarding
 *     description: Mark organization onboarding as complete
 *     tags: [Misc]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               organization_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Onboarding completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 */
router.post(
  "/complete-onboarding",
  validateBody(completeOnboardingRequestSchema),
  async (ctx) => {
    try {
      const payload = ctx.request.body;
      ctx.status = 200;
      ctx.body = { message: "Onboarding complete" };
    } catch (error) {
      ctx.status = 500;
      ctx.body = { error: "Failed to complete onboarding" };
    }
  }
);

/**
 * @swagger
 * /misc/onboarding/market-analysis:
 *   post:
 *     summary: Generate Market Analysis
 *     description: Generate market analysis during onboarding
 *     tags: [Misc]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               organization_id:
 *                 type: string
 *               location_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Analysis started
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 job_id:
 *                   type: string
 */
router.post("/onboarding/market-analysis", async (ctx) => {
  const { competitors, category } = ctx.request.body;

  if (!competitors || !Array.isArray(competitors) || competitors.length === 0) {
    ctx.status = 400;
    ctx.body = {
      error: "Valid competitors array is required",
    };
    return;
  }

  if (!category || typeof category !== "string") {
    ctx.status = 400;
    ctx.body = {
      error: "Valid category is required",
    };
    return;
  }

  try {
    // Use the competitorService for market analysis
    const analysis = await competitorService.performMarketAnalysis(
      competitors,
      category
    );
    ctx.status = 200;
    ctx.body = analysis;
  } catch (error) {
    logger.error("Error in market analysis:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to perform market analysis",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /misc/subscriptions/{organizationId}:
 *   get:
 *     summary: Get Organization Subscription
 *     description: Get subscription details for an organization
 *     tags: [Misc]
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization identifier
 *     responses:
 *       200:
 *         description: Subscription details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 plan:
 *                   type: string
 *                 status:
 *                   type: string
 *                 addons:
 *                   type: array
 *                   items:
 *                     type: string
 */
router.get("/subscriptions/:organizationId", async (ctx) => {
  try {
    const organizationId = parseInt(ctx.params.organizationId);

    if (isNaN(organizationId)) {
      ctx.status = 400;
      ctx.body = { error: "Invalid organization ID" };
      return;
    }

    const subscription = await getStripeSubscriptionById(organizationId);

    if (!subscription) {
      ctx.status = 404;
      ctx.body = { error: "Subscription not found for this organization" };
      return;
    }

    ctx.status = 200;
    ctx.body = { subscription };
  } catch (error) {
    logger.error("Error fetching subscription:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to fetch subscription",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /misc/events/search:
 *   post:
 *     summary: Search Events
 *     description: Search for events by name, city, and/or state. If city/state provided without query, shows local events first (minimum 5 events), supplementing with events from other locations if needed. If query provided, searches across all fields.
 *     tags: [Misc]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               query:
 *                 type: string
 *                 description: Search query for event name, city, or state
 *               city:
 *                 type: string
 *                 description: User's current city for local event filtering
 *               state:
 *                 type: string
 *                 description: User's current state for local event filtering
 *               limit:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 100
 *                 default: 20
 *                 description: Number of results to return per page
 *               page:
 *                 type: integer
 *                 minimum: 1
 *                 default: 1
 *                 description: Page number for pagination
 *     responses:
 *       200:
 *         description: List of matching events
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 events:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       event_id:
 *                         type: string
 *                       event_name:
 *                         type: string
 *                       category:
 *                         type: array
 *                         items:
 *                           type: string
 *                       start_time:
 *                         type: string
 *                       timezone:
 *                         type: string
 *                       host:
 *                         type: string
 *                       starting_price:
 *                         type: string
 *                       address:
 *                         type: string
 *                       city:
 *                         type: string
 *                       state:
 *                         type: string
 *                       postal_code:
 *                         type: string
 *                       image:
 *                         type: string
 *                       url:
 *                         type: string
 *                       source:
 *                         type: string
 *                 total_count:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 total_pages:
 *                   type: integer
 *                 search_context:
 *                   type: string
 *                   description: Indicates whether results are "local", "local_with_supplements", "all_upcoming", or "query_search"
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Server error
 */
router.post(
  "/events/search",
  validateBody(searchEventsRequestSchema),
  async (ctx: Context) => {
    const { query, city, state, limit = 20, page = 1 } = ctx.request.body;

    try {
      const supabaseService = getSupabaseService();

      // If there's a query parameter, search across all fields (event name, city, state)
      if (query && query.trim()) {
        const result = await supabaseService.searchEvents(
          query,
          undefined, // Don't restrict by city/state when searching
          undefined,
          limit,
          page
        );

        ctx.status = 200;
        ctx.body = {
          ...result,
          search_context: "query_search",
        };
        return;
      }

      // If city and state are provided (user's current location), try to find local events first
      if (city && state) {
        const localResult = await supabaseService.searchEvents(
          undefined, // No query
          city,
          state,
          limit,
          page
        );

        // If we found local events but less than 5, supplement with events from other locations
        if (localResult.events && localResult.events.length > 0) {
          if (localResult.events.length >= 5 || page > 1) {
            // We have enough local events or this is a paginated request
            ctx.status = 200;
            ctx.body = {
              ...localResult,
              search_context: "local",
            };
            return;
          } else {
            // We have some local events but less than 5, supplement with more events
            const remainingNeeded = Math.max(0, 5 - localResult.events.length);
            const supplementResult = await supabaseService.searchEvents(
              undefined, // No query
              undefined, // No city filter
              undefined, // No state filter
              remainingNeeded,
              1 // Always get from first page for supplemental events
            );

            // Filter out any events that are already in local results to avoid duplicates
            const localEventIds = new Set(
              localResult.events.map((e) => e.event_id)
            );
            const additionalEvents =
              supplementResult.events?.filter(
                (e) => !localEventIds.has(e.event_id)
              ) || [];

            // Combine local and additional events
            const combinedEvents = [...localResult.events, ...additionalEvents];

            ctx.status = 200;
            ctx.body = {
              events: combinedEvents,
              total_count:
                localResult.total_count + (supplementResult.total_count || 0),
              page,
              limit,
              total_pages: Math.ceil(combinedEvents.length / limit),
              search_context: "local_with_supplements",
            };
            return;
          }
        }

        // If no local events found, fall through to show all upcoming events
        logger.info(
          `No local events found for ${city}, ${state}. Showing all upcoming events.`
        );
      }

      // Show all upcoming events (no location filter)
      // Ensure we get at least 5 events when no query is provided
      const minEventsNeeded = Math.max(limit, 5);
      const allUpcomingResult = await supabaseService.searchEvents(
        undefined, // No query
        undefined, // No city filter
        undefined, // No state filter
        minEventsNeeded,
        page
      );

      ctx.status = 200;
      ctx.body = {
        ...allUpcomingResult,
        search_context: "all_upcoming",
      };
    } catch (error) {
      logger.error("Error searching events:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to search events",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /misc/pos/greencheck/crb/{crbId}/preview:
 *   get:
 *     summary: Preview CRB Data
 *     description: Get a preview of data available from a specific CRB
 *     tags: [Misc]
 *     parameters:
 *       - in: path
 *         name: crbId
 *         required: true
 *         schema:
 *           type: string
 *         description: CRB identifier
 *       - in: header
 *         name: Authorization
 *         required: true
 *         schema:
 *           type: string
 *         description: Bearer token from authentication
 *     responses:
 *       200:
 *         description: Preview of CRB data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 preview:
 *                   type: object
 *                   properties:
 *                     inventory_sample:
 *                       type: array
 *                     sales_sample:
 *                       type: array
 *                     date_range:
 *                       type: object
 */
router.get("/pos/greencheck/crb/:crbId/preview", async (ctx: Context) => {
  const { crbId } = ctx.params;
  const authHeader = ctx.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    ctx.status = 401;
    ctx.body = {
      success: false,
      error: "Authorization header with Bearer token required",
    };
    return;
  }

  const token = authHeader.substring(7);
  const serviceProviderId = "f2b48e57-7114-432d-8f11-a0d74b8fb934";

  try {
    // Get sample data for preview
    const [inventoryResponse, salesResponse] = await Promise.allSettled([
      axios.get(
        `https://sandbox-api.greencheckverified.com/service-providers/${serviceProviderId}/crbs/${crbId}/inventory?limit=5&expand=true`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      ),
      axios.get(
        `https://sandbox-api.greencheckverified.com/service-providers/${serviceProviderId}/crbs/${crbId}/sales?start_date=2024-12-01&end_date=2024-12-31&limit=5`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      ),
    ]);

    const preview = {
      inventory_sample: [],
      sales_sample: [],
      date_range: {
        available_from: "2024-12-01",
        available_to: "2024-12-31",
      },
    };

    if (inventoryResponse.status === "fulfilled") {
      preview.inventory_sample = inventoryResponse.value.data || [];
    }

    if (salesResponse.status === "fulfilled") {
      preview.sales_sample = salesResponse.value.data || [];
    }

    ctx.status = 200;
    ctx.body = {
      success: true,
      crb_id: crbId,
      preview,
    };
  } catch (error: any) {
    logger.error(`Failed to get Green Check CRB preview for ${crbId}:`, error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to get CRB data preview",
      details: error.response?.data || error.message,
    };
  }
});

/// TODO:Move to a greencheck controller

/**
 * @swagger
 * /misc/pos/greencheck/authenticate:
 *   post:
 *     summary: Authenticate with Green Check
 *     description: Get bearer token for Green Check API access
 *     tags: [Misc]
 *     responses:
 *       200:
 *         description: Authentication successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 access_token:
 *                   type: string
 *                 expires_in:
 *                   type: number
 */
router.post("/pos/greencheck/authenticate", async (ctx: Context) => {
  try {
    const response = await axios.post(
      "https://sandbox-api.greencheckverified.com/auth/token",
      {
        client_id: "5fad96d5-4842-4377-ad48-57f7db6b8b2c",
        client_secret:
          "e5ec351d29eff5d0764d5f64d0619604e2e9042447c51d0880d1b58857ead3924bdd8de654456055f5488b0576ba955fb8f7d8f4398df6c9aa621324ccda1a83cb673856b6f0b7b39c24dde770f10cf31a121709772576ba29c517f5d16aabf5d873f1fd291f1959870429cd72bfecab4c63fccd106caaf2ee50dfd2cd20312d",
        grant_type: "client_credentials",
        scope: ["service-provider:read", "point-of-sale:read"]
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    ctx.status = 200;
    ctx.body = {
      success: true,
      access_token: response.data.access_token,
      expires_in: response.data.expires_in,
      token_type: response.data.token_type,
    };
  } catch (error: any) {
    logger.error("Green Check authentication failed:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to authenticate with Green Check",
      details: error.response?.data || error.message,
    };
  }
});

/**
 * @swagger
 * /misc/pos/greencheck/crbs:
 *   get:
 *     summary: Get Available CRBs
 *     description: Fetch list of Cannabis Related Businesses available for connection
 *     tags: [Misc]
 *     parameters:
 *       - in: header
 *         name: Authorization
 *         required: true
 *         schema:
 *           type: string
 *         description: Bearer token from authentication
 *     responses:
 *       200:
 *         description: List of available CRBs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 crbs:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       address:
 *                         type: string
 *                       city:
 *                         type: string
 *                       state:
 *                         type: string
 */
router.get("/pos/greencheck/crbs", async (ctx: Context) => {
  const authHeader = ctx.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    ctx.status = 401;
    ctx.body = {
      success: false,
      error: "Authorization header with Bearer token required",
    };
    return;
  }

  const token = authHeader.substring(7);
  const serviceProviderId = "f2b48e57-7114-432d-8f11-a0d74b8fb934";

  try {
    const response = await axios.get(
      `https://sandbox-api.greencheckverified.com/service-providers/${serviceProviderId}/crbs`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    ctx.status = 200;
    ctx.body = {
      success: true,
      crbs: response.data,
      service_provider_id: serviceProviderId,
    };
  } catch (error: any) {
    logger.error("Failed to fetch Green Check CRBs:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to fetch available locations",
      details: error.response?.data || error.message,
    };
  }
});

/**
 * @swagger
 * /misc/pos/greencheck/crb/{crbId}/validate:
 *   post:
 *     summary: Validate CRB Connection
 *     description: Test connection to a specific CRB and get basic data summary
 *     tags: [Misc]
 *     parameters:
 *       - in: path
 *         name: crbId
 *         required: true
 *         schema:
 *           type: string
 *         description: CRB identifier to validate
 *       - in: header
 *         name: Authorization
 *         required: true
 *         schema:
 *           type: string
 *         description: Bearer token from authentication
 *     responses:
 *       200:
 *         description: CRB validation result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 crb_info:
 *                   type: object
 *                 data_summary:
 *                   type: object
 *                   properties:
 *                     inventory_count:
 *                       type: number
 *                     recent_sales_count:
 *                       type: number
 *                     customer_count:
 *                       type: number
 */
router.post("/pos/greencheck/crb/:crbId/validate", async (ctx: Context) => {
  const { crbId } = ctx.params;
  const authHeader = ctx.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    ctx.status = 401;
    ctx.body = {
      success: false,
      error: "Authorization header with Bearer token required",
    };
    return;
  }

  const token = authHeader.substring(7);
  const serviceProviderId = "f2b48e57-7114-432d-8f11-a0d74b8fb934";

  try {
    // Test multiple endpoints to validate connection and get data summary
    const [inventoryResponse, salesResponse] = await Promise.allSettled([
      // Get inventory with limit to test connection
      axios.get(
        `https://sandbox-api.greencheckverified.com/service-providers/${serviceProviderId}/crbs/${crbId}/inventory?limit=1`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      ),
      // Get recent sales to test connection
      axios.get(
        `https://sandbox-api.greencheckverified.com/service-providers/${serviceProviderId}/crbs/${crbId}/sales?start_date=2024-12-01&end_date=2024-12-31&limit=1`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      ),
    ]);

    const dataSummary = {
      inventory_count: 0,
      recent_sales_count: 0,
      connection_status: "connected",
    };

    // Process inventory response
    if (inventoryResponse.status === "fulfilled") {
      dataSummary.inventory_count = inventoryResponse.value.data?.length || 0;
    }

    // Process sales response
    if (salesResponse.status === "fulfilled") {
      dataSummary.recent_sales_count = salesResponse.value.data?.length || 0;
    }

    ctx.status = 200;
    ctx.body = {
      success: true,
      crb_id: crbId,
      service_provider_id: serviceProviderId,
      data_summary: dataSummary,
      message: "CRB connection validated successfully",
    };
  } catch (error: any) {
    logger.error(`Failed to validate Green Check CRB ${crbId}:`, error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to validate CRB connection",
      details: error.response?.data || error.message,
    };
  }
});

/**
 * @swagger
 * /misc/pos/greencheck/pos/{posSystemId}/check:
 *   get:
 *     summary: Check POS Connection Status
 *     description: Check if user is already connected to a specific POS system through Green Check
 *     tags: [Misc]
 *     parameters:
 *       - in: path
 *         name: posSystemId
 *         required: true
 *         schema:
 *           type: string
 *         description: POS system identifier (cova, biotrack, greenbits, etc.)
 *       - in: header
 *         name: Authorization
 *         required: true
 *         schema:
 *           type: string
 *         description: Bearer token from authentication
 *     responses:
 *       200:
 *         description: Connection status result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 connected:
 *                   type: boolean
 *                 connection:
 *                   type: object
 *                   nullable: true
 */
router.get("/pos/greencheck/pos/:posSystemId/check", async (ctx: Context) => {
  const { posSystemId } = ctx.params;
  const authHeader = ctx.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    ctx.status = 401;
    ctx.body = {
      success: false,
      error: "Authorization header with Bearer token required",
    };
    return;
  }

  try {
    // For sandbox, we'll simulate that users are not connected initially
    // In production, this would check Green Check's API for existing connections
    ctx.status = 200;
    ctx.body = {
      connected: false,
      connection: null,
      pos_system: posSystemId,
      message: `No existing connection found for ${posSystemId}`,
    };
  } catch (error: any) {
    logger.error(`Failed to check ${posSystemId} connection status:`, error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to check connection status",
      details: error.message,
    };
  }
});

/**
 * @swagger
 * /misc/pos/greencheck/pos/{posSystemId}/schema:
 *   get:
 *     summary: Get POS Credential Schema
 *     description: Get the required credential fields for connecting to a specific POS system
 *     tags: [Misc]
 *     parameters:
 *       - in: path
 *         name: posSystemId
 *         required: true
 *         schema:
 *           type: string
 *         description: POS system identifier
 *       - in: header
 *         name: Authorization
 *         required: true
 *         schema:
 *           type: string
 *         description: Bearer token from authentication
 *     responses:
 *       200:
 *         description: Credential schema for the POS system
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 fields:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                       label:
 *                         type: string
 *                       type:
 *                         type: string
 *                       required:
 *                         type: boolean
 *                       description:
 *                         type: string
 */
router.get("/pos/greencheck/pos/:posSystemId/schema", async (ctx: Context) => {
  const { posSystemId } = ctx.params;
  const authHeader = ctx.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    ctx.status = 401;
    ctx.body = {
      success: false,
      error: "Authorization header with Bearer token required",
    };
    return;
  }

  try {
    // Define credential schemas for different POS systems
    const credentialSchemas: Record<string, any[]> = {
      cova: [
        {
          name: "company_id",
          label: "Company ID",
          type: "number",
          required: true,
          description: "Your Cova company identifier",
        },
        {
          name: "location_id",
          label: "Location ID",
          type: "number",
          required: true,
          description: "Your Cova location identifier",
        },
        {
          name: "bearer_token",
          label: "Bearer Token",
          type: "password",
          required: true,
          description: "Your Cova API bearer token",
        },
      ],
      biotrack: [
        {
          name: "api_key",
          label: "API Key",
          type: "password",
          required: true,
          description: "Your BioTrack API key",
        },
        {
          name: "location_id",
          label: "Location ID",
          type: "text",
          required: true,
          description: "Your BioTrack location identifier",
        },
      ],
      greenbits: [
        {
          name: "api_key",
          label: "API Key",
          type: "password",
          required: true,
          description: "Your Greenbits API key",
        },
        {
          name: "store_id",
          label: "Store ID",
          type: "text",
          required: true,
          description: "Your Greenbits store identifier",
        },
      ],
      treez: [
        {
          name: "api_key",
          label: "API Key",
          type: "password",
          required: true,
          description: "Your Treez API key",
        },
        {
          name: "retailer_id",
          label: "Retailer ID",
          type: "text",
          required: true,
          description: "Your Treez retailer identifier",
        },
      ],
    };

    const fields = credentialSchemas[posSystemId] || [];

    ctx.status = 200;
    ctx.body = {
      success: true,
      pos_system: posSystemId,
      fields,
    };
  } catch (error: any) {
    logger.error(`Failed to get credential schema for ${posSystemId}:`, error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to get credential schema",
      details: error.message,
    };
  }
});

/**
 * @swagger
 * /misc/pos/greencheck/crb/create:
 *   post:
 *     summary: Create CRB with POS Credentials
 *     description: Create a new Cannabis Related Business connection with POS credentials
 *     tags: [Misc]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               pos_system:
 *                 type: string
 *               credentials:
 *                 type: object
 *     responses:
 *       200:
 *         description: CRB created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 crb:
 *                   type: object
 */
router.post("/pos/greencheck/crb/create", async (ctx: Context) => {
  const authHeader = ctx.headers.authorization;
  const { pos_system, credentials } = ctx.request.body;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    ctx.status = 401;
    ctx.body = {
      success: false,
      error: "Authorization header with Bearer token required",
    };
    return;
  }

  if (!pos_system || !credentials) {
    ctx.status = 400;
    ctx.body = {
      success: false,
      error: "pos_system and credentials are required",
    };
    return;
  }

  try {
    // For sandbox, simulate creating a CRB
    // In production, this would call Green Check's API to create the actual connection
    const crbId = `crb_${pos_system}_${Date.now()}`;

    const mockCrb = {
      id: crbId,
      name: `${
        pos_system.charAt(0).toUpperCase() + pos_system.slice(1)
      } Location`,
      pos_system,
      status: "connected",
      created_at: new Date().toISOString(),
      connection_details: {
        // Don't return sensitive credentials
        pos_system,
        connected_at: new Date().toISOString(),
      },
    };

    // Log the connection attempt (without sensitive data)
    logger.info(`Created Green Check CRB for ${pos_system}:`, {
      crb_id: crbId,
      pos_system,
      credentials_provided: Object.keys(credentials),
    });

    ctx.status = 200;
    ctx.body = {
      success: true,
      crb: mockCrb,
      message: `Successfully connected to ${pos_system} through Green Check`,
    };
  } catch (error: any) {
    logger.error("Failed to create Green Check CRB:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to create CRB connection",
      details: error.message,
    };
  }
});

/**
 * @swagger
 * /misc/pos/greencheck/crb/{crbId}/preview:
 *   get:
 *     summary: Preview CRB Data
 *     description: Get a preview of data available from a specific CRB
 *     tags: [Misc]
 *     parameters:
 *       - in: path
 *         name: crbId
 *         required: true
 *         schema:
 *           type: string
 *         description: CRB identifier
 *       - in: header
 *         name: Authorization
 *         required: true
 *         schema:
 *           type: string
 *         description: Bearer token from authentication
 *     responses:
 *       200:
 *         description: Preview of CRB data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 preview:
 *                   type: object
 *                   properties:
 *                     inventory_sample:
 *                       type: array
 *                     sales_sample:
 *                       type: array
 *                     date_range:
 *                       type: object
 */
router.get("/pos/greencheck/crb/:crbId/preview", async (ctx: Context) => {
  const { crbId } = ctx.params;
  const authHeader = ctx.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    ctx.status = 401;
    ctx.body = {
      success: false,
      error: "Authorization header with Bearer token required",
    };
    return;
  }

  const token = authHeader.substring(7);
  const serviceProviderId = "f2b48e57-7114-432d-8f11-a0d74b8fb934";

  try {
    // Get sample data for preview
    const [inventoryResponse, salesResponse] = await Promise.allSettled([
      axios.get(
        `https://sandbox-api.greencheckverified.com/service-providers/${serviceProviderId}/crbs/${crbId}/inventory?limit=5&expand=true`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      ),
      axios.get(
        `https://sandbox-api.greencheckverified.com/service-providers/${serviceProviderId}/crbs/${crbId}/sales?start_date=2024-12-01&end_date=2024-12-31&limit=5`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      ),
    ]);

    const preview = {
      inventory_sample: [],
      sales_sample: [],
      date_range: {
        available_from: "2024-12-01",
        available_to: "2024-12-31",
      },
    };

    if (inventoryResponse.status === "fulfilled") {
      preview.inventory_sample = inventoryResponse.value.data || [];
    }

    if (salesResponse.status === "fulfilled") {
      preview.sales_sample = salesResponse.value.data || [];
    }

    ctx.status = 200;
    ctx.body = {
      success: true,
      crb_id: crbId,
      preview,
    };
  } catch (error: any) {
    logger.error(`Failed to get Green Check CRB preview for ${crbId}:`, error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to get CRB data preview",
      details: error.response?.data || error.message,
    };
  }
});

export default router;
