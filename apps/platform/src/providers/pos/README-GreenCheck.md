# Green Check POS Provider

This provider integrates with the Green Check Verified API to sync sales transaction data from multiple POS systems through a unified interface.

## Features

- **Multi-POS Support**: Access data from BioTrack, Cova, Dutchie, Treez, Greenbits, and other POS systems
- **Sales Transaction Sync**: Fetches detailed sales data with line items, customer info, and payment details
- **Customer Data**: Syncs customer profiles including medical/recreational status
- **Location Management**: Manages Cannabis Related Business (CRB) locations
- **Real-time Verification**: Tests API connection during setup
- **Inventory Support**: Optional inventory data synchronization

## Setup

### 1. Get Green Check API Credentials

1. Contact Green Check Verified to get API access
2. Obtain your:
   - Client ID
   - Client Secret
   - Service Provider ID
3. Get the CRB (Cannabis Related Business) ID for each location

### 2. Configure Environment Variables

Add these variables to your `.env` file:

```bash
# Green Check POS Integration
GREEN_CHECK_CLIENT_ID=your_client_id_here
GREEN_CHECK_CLIENT_SECRET=your_client_secret_here
GREEN_CHECK_SERVICE_PROVIDER_ID=your_service_provider_id_here
GREEN_CHECK_BASE_URL=https://sandbox-api.greencheckverified.com
GREEN_CHECK_ENVIRONMENT=sandbox
```

For production, change:
```bash
GREEN_CHECK_BASE_URL=https://api.greencheckverified.com
GREEN_CHECK_ENVIRONMENT=production
```

### 3. Configure the Provider

```typescript
import GreenCheckProvider from "./providers/pos/GreenCheckProvider";

const greenCheckConfig = {
  crbId: "your-crb-id-here", // Specific to each location
};

const provider = new GreenCheckProvider(greenCheckConfig, locationId);
```

### 4. Initialize and Verify

```typescript
// Boot the provider
provider.boot();

// Verify connection
const isConnected = await provider.verify();
if (isConnected) {
  console.log("Successfully connected to Green Check");
} else {
  console.error("Failed to connect to Green Check");
}
```

## Usage

### Fetch Sales Transactions

```typescript
const startDate = new Date("2024-01-01");
const endDate = new Date("2024-01-31");

const tickets = await provider.fetchTickets(startDate, endDate);
console.log(`Fetched ${tickets.length} transactions`);
```

### Sync Sales Data to Database

```typescript
const startDate = new Date("2024-01-01");
const endDate = new Date("2024-01-31");

await provider.syncSales(startDate, endDate);
console.log("Sales data synced successfully");
```

### Sync Customer Data

```typescript
await provider.syncCustomers();
console.log("Customer data synced successfully");
```

### Sync Location Information

```typescript
await provider.syncLocation();
console.log("Location data synced successfully");
```

## API Endpoints Used

| Endpoint | Purpose | Data Retrieved |
|----------|---------|----------------|
| `/auth/token` | Authentication | Access token |
| `/service-providers/{id}/crbs` | Location discovery | Available CRB locations |
| `/service-providers/{id}/crbs/{crb_id}/sales` | Sales sync | Transaction data with line items |
| `/service-providers/{id}/crbs/{crb_id}/customers` | Customer sync | Customer profiles |
| `/service-providers/{id}/crbs/{crb_id}/inventory` | Inventory sync | Stock levels |

## Data Mapping

### Sales Transaction → PosTicket

- `id` → Green Check sale ID
- `date` → Transaction date
- `total` → Total amount paid
- `customer_name` → Customer identifier
- `customer_type` → "medical" or "rec"
- `budtender_name` → Employee ID
- `tax_amount` → Tax paid
- `discounted_amount` → Total discounts applied
- `items` → Line items with product details

### Line Items → PosTicketItem

- `id` → POS product ID
- `name` → Product name (with fallback)
- `quantity` → Number of units
- `price` → Price per unit
- `category` → Product type
- `grams` → Weight for cannabis products

## Testing

Run the integration test:

```bash
cd apps/platform
node tests/test-green-check-integration.js
```

This will test:
- API connection
- Sales data fetching
- Data transformation
- Database sync logic

## Supported POS Systems

Green Check provides unified access to:

- **BioTrack** - Cannabis tracking and POS
- **Cova** - Cannabis retail POS
- **Dutchie** - Cannabis e-commerce and POS
- **Treez** - Cannabis retail management
- **Greenbits** - Cannabis POS and compliance
- **Flowhub** - Cannabis retail platform
- **MJ Freeway** - Cannabis business management
- **POSaBIT** - Cannabis payment processing
- **Proteus420** - Cannabis compliance and POS
- **Alleaves** - Cannabis retail software
- **Meadow** - Cannabis retail platform
- **GrowFlow** - Cannabis cultivation and retail

## Error Handling

The provider includes comprehensive error handling:

- API authentication failures
- Network connectivity issues
- Data validation errors
- Duplicate record prevention
- Graceful degradation for missing data

## Security Notes

- API credentials are stored in environment variables
- Tokens are automatically refreshed
- All API calls use HTTPS
- Customer data is handled according to privacy regulations

## Limitations

- Products are extracted from sales data (no dedicated product endpoint)
- Some POS-specific fields may not be available
- Rate limiting may apply to API calls
- Historical data availability depends on POS system retention
