# Dutchie POS Provider

This provider integrates with the Dutchie Point of Sale API to sync transaction data, customer information, products, and inventory.

## Features

- **Transaction Sync**: Fetches sales transactions with detailed line items
- **Customer Sync**: Syncs customer profiles including medical/recreational status
- **Product Sync**: Retrieves product catalog with pricing and inventory
- **Inventory Sync**: Gets current inventory levels and lab results
- **Real-time Verification**: Tests API connection during setup

## Setup

### 1. Get Dutchie API Credentials

1. Log into your Dutchie POS dashboard
2. Navigate to **Settings** → **API Access**
3. Generate or copy your API Key
4. (Optional) Note your Consumer Key if required for your integration

### 2. Configure the Provider

```typescript
import DutchieProvider from "./providers/pos/DutchieProvider";

const dutchieConfig = {
  apiKey: "your-dutchie-api-key",
  consumerKey: "your-consumer-key", // Optional
};

const provider = new DutchieProvider(dutchieConfig, locationId);
```

### 3. Initialize and Verify

```typescript
// Boot the provider
provider.boot();

// Verify connection
const isConnected = await provider.verify();
if (isConnected) {
  console.log("Successfully connected to Dutchie");
} else {
  console.error("Failed to connect to Dutchie");
}
```

## Usage

### Fetch Transactions

```typescript
const startDate = new Date("2024-01-01");
const endDate = new Date("2024-01-31");

const tickets = await provider.fetchTickets(startDate, endDate);
console.log(`Fetched ${tickets.length} transactions`);
```

### Fetch Products

```typescript
const products = await provider.fetchProducts();
console.log(`Fetched ${products.length} products`);
```

### Sync Customer Data

```typescript
await provider.syncCustomers();
console.log("Customer data synced successfully");
```

### Sync Inventory

```typescript
await provider.syncInventory();
console.log("Inventory data synced successfully");
```

## API Endpoints Used

| Endpoint                  | Purpose          | Data Retrieved               |
| ------------------------- | ---------------- | ---------------------------- |
| `/whoami`                 | Verification     | Location identity            |
| `/reporting/transactions` | Transaction sync | Sales data with line items   |
| `/reporting/customers`    | Customer sync    | Customer profiles            |
| `/products`               | Product sync     | Product catalog              |
| `/inventory`              | Inventory sync   | Stock levels and lab results |

## Data Mapping

### Transaction → PosTicket

- `transactionId` → `id`
- `transactionDate` → `date`
- `total` → `total`
- `customerId` → `customer_id`
- `cashPaid` → `amount_paid_in_cash`
- `debitPaid + electronicPaid` → `amount_paid_in_debit`
- `tax` → `tax_amount`
- `totalDiscount` → `discounted_amount`

### Customer → User Record

- `customerId` → `pos_customer_id`
- `firstName/lastName` → `first_name/last_name`
- `emailAddress` → `email`
- `phone/cellPhone` → `phone/cell_phone`
- `isMedical` → Determines customer type
- `isLoyaltyMember` → `loyalty_member`

### Product → PosProduct

- `productId` → `id`
- `productName` → `name`
- `category` → `category`
- `price` → `price`
- `sku` → `sku`
- `brandName` → `brand`
- `vendorName` → `vendor`

## Error Handling

The provider includes comprehensive error handling for:

- **Authentication errors** (401): Invalid API key
- **Authorization errors** (403): Insufficient permissions
- **Network errors**: Connection issues
- **Data validation errors**: Malformed responses
- **Rate limiting**: API quota exceeded

All errors are logged with context for debugging.

## Security

- API keys are securely stored and transmitted
- HTTPS is used for all API communications
- Sensitive data is encrypted in transit and at rest
- API credentials can be rotated without affecting historical data

## UI Integration

The provider includes a React component (`DutchieIntegration`) for easy setup:

```tsx
import DutchieIntegration from "./components/onboarding/DutchieIntegration";

<DutchieIntegration
  onConnectionSuccess={(apiKey, locationInfo) => {
    // Handle successful connection
  }}
  onConnectionError={(error) => {
    // Handle connection error
  }}
  onCancel={() => {
    // Handle cancellation
  }}
/>;
```

## Troubleshooting

### Common Issues

1. **Invalid API Key**: Verify the API key in your Dutchie dashboard
2. **403 Forbidden**: Check API permissions for your account
3. **Rate Limiting**: Implement retry logic with exponential backoff
4. **Missing Data**: Ensure all required fields are populated in Dutchie

### Debug Mode

Enable detailed logging:

```typescript
import { logger } from "../../config/logger";

logger.level = "debug";
```

## Support

For issues specific to this provider:

1. Check the application logs for detailed error messages
2. Verify API credentials and permissions
3. Test the connection using the UI integration component

For Dutchie API issues:

1. Check the [Dutchie API Documentation](https://api.pos.dutchie.com)
2. Contact Dutchie support for API-related problems
3. Verify your account has API access enabled
