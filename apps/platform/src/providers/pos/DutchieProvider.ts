import PosProvider, {
  PosTicket,
  PosTicketItem,
  PosProduct,
} from "./PosProvider";
import { PosData } from "../../pos/PosData";
import { User } from "../../users/User";
import App from "../../app";
import { logger } from "../../config/logger";
import axios, { AxiosInstance } from "axios";

interface DutchieConfig {
  apiKey: string;
  consumerKey?: string;
}

interface DutchieTransaction {
  transactionId: number;
  customerId: number;
  employeeId: number;
  transactionDate: string;
  voidDate?: string;
  isVoid: boolean;
  subtotal: number;
  totalDiscount: number;
  tax: number;
  tipAmount: number;
  total: number;
  paid: number;
  changeDue: number;
  totalItems: number;
  terminalName: string;
  checkInDate?: string;
  invoiceNumber: string;
  isTaxInclusive: boolean;
  transactionType: string;
  loyaltyEarned: number;
  loyaltySpent: number;
  lastModifiedDateUTC: string;
  cashPaid: number;
  debitPaid: number;
  electronicPaid: number;
  creditPaid: number;
  giftPaid: number;
  mmapPaid: number;
  customerTypeId: number;
  isMedical: boolean;
  orderType?: string;
  wasPreOrdered: boolean;
  orderSource?: string;
  completedByUser: string;
  items: DutchieTransactionItem[];
}

interface DutchieTransactionItem {
  transactionId: number;
  productId: number;
  totalPrice: number;
  quantity: number;
  unitPrice: number;
  unitCost: number;
  packageId?: string;
  sourcePackageId?: string;
  totalDiscount: number;
  inventoryId: number;
  unitId: number;
  unitWeight: number;
  unitWeightUnit: string;
  flowerEquivalent: number;
  flowerEquivalentUnit: string;
  returnDate?: string;
  isReturned: boolean;
  returnedByTransactionId?: number;
  returnReason?: string;
  batchName?: string;
  vendor?: string;
  isCoupon: boolean;
}

interface DutchieCustomer {
  customerId: number;
  uniqueId: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  middleName?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  phone?: string;
  cellPhone?: string;
  emailAddress?: string;
  status: string;
  mmjidNumber?: string;
  mmjidExpirationDate?: string;
  lastModifiedDateUTC: string;
  creationDate: string;
  customerType: string;
  gender?: string;
  dateOfBirth?: string;
  externalCustomerId?: string;
  isAnonymous: boolean;
  referralSource?: string;
  isLoyaltyMember: boolean;
}

interface DutchieProduct {
  productId: number;
  sku: string;
  internalName?: string;
  productName: string;
  description?: string;
  masterCategory?: string;
  categoryId: number;
  category: string;
  imageUrl?: string;
  strainId?: number;
  strain?: string;
  strainType?: string;
  size?: string;
  netWeight?: number;
  netWeightUnit?: string;
  brandId?: number;
  brandName?: string;
  vendorId?: number;
  vendorName?: string;
  isCannabis: boolean;
  isActive: boolean;
  thcContent?: number;
  thcContentUnit?: string;
  cbdContent?: number;
  cbdContentUnit?: string;
  price: number;
  medPrice?: number;
  recPrice?: number;
  unitCost: number;
  unitType?: string;
  onlineAvailable: boolean;
  lowInventoryThreshold?: number;
  lastModifiedDateUTC: string;
}

export default class DutchieProvider extends PosProvider {
  private config: DutchieConfig;
  private api: AxiosInstance;

  constructor(config: DutchieConfig, location_id: number) {
    super();
    this.location_id = location_id;
    this.config = config;

    this.api = axios.create({
      baseURL: "https://api.pos.dutchie.com",
      headers: {
        "Content-Type": "application/json",
        Authorization: config.apiKey,
        ...(config.consumerKey && { ConsumerKey: config.consumerKey }),
      },
    });
  }

  boot(): void {
    logger.info({
      message: "Dutchie POS Provider initialized",
      location_id: this.location_id,
    });
  }

  async verify(): Promise<boolean> {
    try {
      // Verify by calling the whoami endpoint
      const response = await this.api.get("/whoami");
      return response.status === 200;
    } catch (error) {
      logger.error({
        message: "Dutchie provider verification failed",
        error: error instanceof Error ? error.message : "Unknown error",
        location_id: this.location_id,
      });
      return false;
    }
  }

  async fetchTickets(startDate: Date, endDate: Date): Promise<PosTicket[]> {
    try {
      const response = await this.api.get("/reporting/transactions", {
        params: {
          FromDateUTC: startDate.toISOString(),
          ToDateUTC: endDate.toISOString(),
          IncludeDetail: true,
          IncludeTaxes: true,
        },
      });

      const transactions: DutchieTransaction[] = response.data;
      return transactions
        .filter((transaction) => !transaction.isVoid)
        .map((transaction) => this.convertTransactionToTicket(transaction));
    } catch (error) {
      logger.error({
        message: "Failed to fetch Dutchie transactions",
        error: error instanceof Error ? error.message : "Unknown error",
        location_id: this.location_id,
      });
      throw error;
    }
  }

  async fetchProducts(): Promise<PosProduct[]> {
    try {
      const response = await this.api.get("/products", {
        params: {
          isActive: true,
        },
      });

      const products: DutchieProduct[] = response.data;
      return products.map((product) => this.convertDutchieProduct(product));
    } catch (error) {
      logger.error({
        message: "Failed to fetch Dutchie products",
        error: error instanceof Error ? error.message : "Unknown error",
        location_id: this.location_id,
      });
      throw error;
    }
  }

  // Extended Dutchie specific methods
  async syncCustomers(): Promise<void> {
    try {
      const response = await this.api.get("/reporting/customers", {
        params: {
          includeAnonymous: false,
        },
      });

      const customers: DutchieCustomer[] = response.data;

      for (const customer of customers) {
        await this.syncCustomer(customer);
      }

      logger.info({
        message: "Successfully synced Dutchie customers",
        location_id: this.location_id,
        count: customers.length,
      });
    } catch (error) {
      logger.error({
        message: "Failed to sync Dutchie customers",
        error: error instanceof Error ? error.message : "Unknown error",
        location_id: this.location_id,
      });
      throw error;
    }
  }

  async syncInventory(): Promise<void> {
    try {
      const response = await this.api.get("/inventory", {
        params: {
          includeLabResults: true,
          includeRoomQuantities: true,
        },
      });

      // Process inventory data
      logger.info({
        message: "Successfully synced Dutchie inventory",
        location_id: this.location_id,
        count: response.data.length,
      });
    } catch (error) {
      logger.error({
        message: "Failed to sync Dutchie inventory",
        error: error instanceof Error ? error.message : "Unknown error",
        location_id: this.location_id,
      });
      throw error;
    }
  }

  private convertTransactionToTicket(
    transaction: DutchieTransaction
  ): PosTicket {
    const items: PosTicketItem[] = transaction.items.map((item) => ({
      id: item.productId.toString(),
      name: `Product ${item.productId}`, // Product name would need to be resolved from products
      quantity: item.quantity,
      price: item.unitPrice,
      category: "", // Would need to be resolved from product data
      subcategory: "",
      cost: item.unitCost,
    }));

    return {
      id: transaction.transactionId.toString(),
      date: new Date(transaction.transactionDate),
      total: transaction.total,
      customer_name: "", // Would need to be resolved from customer data
      customer_id: transaction.customerId.toString(),
      customer_type: transaction.isMedical ? "medical" : "recreational",
      customer_birth_date: undefined, // Would need to be resolved from customer data
      budtender_name: transaction.completedByUser || "",
      location_name: "", // Would need to be resolved from location data
      returned_amount: transaction.isVoid ? transaction.total : 0,
      discounted_amount: transaction.totalDiscount,
      loyalty_as_discount: 0, // Calculate from loyalty spent if needed
      loyalty_as_payment: 0,
      tax_amount: transaction.tax,
      amount_paid_in_cash: transaction.cashPaid,
      amount_paid_in_debit: transaction.debitPaid + transaction.electronicPaid,
      inventory_cost: items.reduce(
        (sum, item) => sum + (item.cost || 0) * item.quantity,
        0
      ),
      inventory_profit:
        transaction.total -
        transaction.tax -
        items.reduce((sum, item) => sum + (item.cost || 0) * item.quantity, 0),
      items,
    };
  }

  private convertDutchieProduct(product: DutchieProduct): PosProduct {
    return {
      id: product.productId.toString(),
      name: product.productName,
      description: product.description || "",
      category: product.category,
      subcategory: product.masterCategory || "",
      price: product.price,
      inventory_quantity: 0, // Would be updated from inventory sync
      sku: product.sku,
      barcode: "", // Not available in Dutchie API
      brand: product.brandName || "",
      vendor: product.vendorName || "",
      is_active: product.isActive,
    };
  }

  private async syncCustomer(customer: DutchieCustomer): Promise<void> {
    try {
      // Check if customer already exists
      const user = await App.main
        .db("users")
        .where({
          location_id: this.location_id,
          pos_customer_id: customer.customerId.toString(),
        })
        .first();

      const userData = {
        location_id: this.location_id,
        pos_customer_id: customer.customerId.toString(),
        unique_id: customer.uniqueId,
        first_name: customer.firstName,
        last_name: customer.lastName,
        middle_name: customer.middleName,
        email: customer.emailAddress,
        phone: customer.phone,
        cell_phone: customer.cellPhone,
        birth_date: customer.dateOfBirth
          ? new Date(customer.dateOfBirth)
          : null,
        address_1: customer.address1,
        address_2: customer.address2,
        city: customer.city,
        state: customer.state,
        zip_code: customer.postalCode,
        gender: customer.gender,
        pos_customer_type: customer.customerType,
        mmj_id: customer.mmjidNumber,
        mmj_id_expiration_date: customer.mmjidExpirationDate
          ? new Date(customer.mmjidExpirationDate)
          : null,
        loyalty_member: customer.isLoyaltyMember,
        anonymous: customer.isAnonymous,
        referral_source: customer.referralSource,
        status: customer.status,
        external_customer_id: customer.externalCustomerId,
        pos_updated_date: new Date(customer.lastModifiedDateUTC),
        created_at: new Date(customer.creationDate),
        updated_at: new Date(),
      };

      if (user) {
        // Update existing user
        await App.main.db("users").where({ id: user.id }).update(userData);
      } else {
        // Create new user
        await App.main.db("users").insert({
          ...userData,
          created_at: new Date(),
        });
      }
    } catch (error) {
      logger.error({
        message: "Failed to sync Dutchie customer",
        customer_id: customer.customerId,
        error: error instanceof Error ? error.message : "Unknown error",
        location_id: this.location_id,
      });
    }
  }

  // Override the base method to use Dutchie specific data transformation
  normalizeTicketToPosData(ticket: PosTicket): Partial<PosData> {
    const baseData = super.normalizeTicketToPosData(ticket);

    // Add Dutchie specific fields
    return {
      ...baseData,
      customer_id: ticket.customer_id,
      customer_email: undefined, // Will be resolved from customer sync
      customer_phone: undefined, // Will be resolved from customer sync
    };
  }
}
