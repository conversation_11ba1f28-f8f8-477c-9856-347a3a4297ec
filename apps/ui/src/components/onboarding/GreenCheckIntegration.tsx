import React, { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import api from "../../api";

interface PosSystem {
  id: string;
  name: string;
  description: string;
  manual_intervention_required: boolean;
  integration_docs_url?: string;
}

interface PosCredentialField {
  name: string;
  label: string;
  type: "text" | "password" | "number";
  required: boolean;
  description?: string;
}

interface GreenCheckIntegrationProps {
  onConnectionSuccess: (crbId: string, crbInfo: any) => void;
  onConnectionError: (error: string) => void;
  onCancel: () => void;
}

const AVAILABLE_POS_SYSTEMS: PosSystem[] = [
  {
    id: "cova",
    name: "Cova",
    description: "Connect to your Cova POS system",
    manual_intervention_required: false,
  },
  {
    id: "biotrack",
    name: "BioTrack",
    description: "Connect to your BioTrack system",
    manual_intervention_required: false,
  },
  {
    id: "greenbits",
    name: "Greenbits",
    description: "Connect to your Greenbits POS",
    manual_intervention_required: false,
  },
  {
    id: "treez",
    name: "<PERSON><PERSON>",
    description: "Connect to your Treez POS system",
    manual_intervention_required: false,
  },
  {
    id: "mjfreeway",
    name: "MJFreeway",
    description: "Connect to your MJFreeway system",
    manual_intervention_required: true,
    integration_docs_url: "https://docs.mjfreeway.com/integration",
  },
  {
    id: "sweed",
    name: "Sweed",
    description: "Connect to your Sweed POS system",
    manual_intervention_required: true,
    integration_docs_url: "https://docs.sweedco.com/api",
  },
];

export default function GreenCheckIntegration({
  onConnectionSuccess,
  onConnectionError,
  onCancel,
}: GreenCheckIntegrationProps) {
  const [step, setStep] = useState<
    | "authenticating"
    | "selecting-pos"
    | "checking-connection"
    | "collecting-credentials"
    | "creating-crb"
    | "completed"
  >("authenticating");
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [selectedPos, setSelectedPos] = useState<PosSystem | null>(null);
  const [existingConnection, setExistingConnection] = useState<any>(null);
  const [credentialFields, setCredentialFields] = useState<
    PosCredentialField[]
  >([]);
  const [credentials, setCredentials] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [createdCrb, setCreatedCrb] = useState<any>(null);

  // Step 1: Authenticate with Green Check
  useEffect(() => {
    authenticateWithGreenCheck();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const authenticateWithGreenCheck = async () => {
    try {
      setLoading(true);
      setError(null);
      toast.loading("Connecting to Green Check...", { id: "greencheck-auth" });

      const response = await api.misc.greenCheck.authenticate();

      if (response.success) {
        setAccessToken(response.access_token);
        setStep("selecting-pos");
        toast.success("Connected to Green Check!", { id: "greencheck-auth" });
      } else {
        throw new Error(response.error || "Failed to authenticate");
      }
    } catch (error: any) {
      const errorMsg =
        error.response?.data?.error || error.message || "Authentication failed";
      setError(errorMsg);
      toast.error(errorMsg, { id: "greencheck-auth" });
      onConnectionError(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  // Step 2: Check existing connection for selected POS
  const checkExistingConnection = async (posSystem: PosSystem) => {
    try {
      setLoading(true);
      setSelectedPos(posSystem);
      setStep("checking-connection");
      setError(null);
      toast.loading(`Checking ${posSystem.name} connection...`, {
        id: "greencheck-check",
      });

      if (!accessToken) {
        setError("Missing access token. Please try again.");
        toast.error("Missing access token. Please try again.", { id: "greencheck-check" });
        setLoading(false);
        return;
      }
      const response = await api.misc.greenCheck.checkPosConnection(
        posSystem.id,
        accessToken
      );

      if (response.connected) {
        // Already connected
        setExistingConnection(response.connection);
        setCreatedCrb(response.connection);
        setStep("completed");
        toast.success(`Already connected to ${posSystem.name}!`, {
          id: "greencheck-check",
        });
      } else {
        // Not connected, need to collect credentials
        if (posSystem.manual_intervention_required) {
          // Show manual intervention message
          setStep("collecting-credentials");
          setCredentialFields([]);
          toast.dismiss("greencheck-check");
        } else {
          // Get credential fields for this POS
          if (!accessToken) {
            setError("Missing access token. Please try again.");
            toast.error("Missing access token. Please try again.", { id: "greencheck-check" });
            setLoading(false);
            return;
          }
          const schemaResponse =
            await api.misc.greenCheck.getPosCredentialSchema(
              posSystem.id,
              accessToken
            );
          setCredentialFields(schemaResponse.fields || []);
          setStep("collecting-credentials");
          toast.success(`Ready to connect to ${posSystem.name}`, {
            id: "greencheck-check",
          });
        }
      }
    } catch (error: any) {
      const errorMsg =
        error.response?.data?.error ||
        error.message ||
        "Connection check failed";
      setError(errorMsg);
      toast.error(errorMsg, { id: "greencheck-check" });
    } finally {
      setLoading(false);
    }
  };

  // Step 3: Create CRB with collected credentials
  const createCrb = async () => {
    if (!selectedPos || !accessToken) {
      setError("Missing POS selection or access token.");
      toast.error("Missing POS selection or access token.", { id: "greencheck-create" });
      return;
    }
    try {
      setLoading(true);
      setStep("creating-crb");
      setError(null);
      toast.loading(`Creating connection to ${selectedPos.name}...`, {
        id: "greencheck-create",
      });

      const response = await api.misc.greenCheck.createCrb(
        {
          pos_system: selectedPos.id,
          credentials: credentials,
        },
        accessToken
      );

      if (response.success) {
        setCreatedCrb(response.crb);
        setStep("completed");
        toast.success(`Successfully connected to ${selectedPos.name}!`, {
          id: "greencheck-create",
        });
      } else {
        throw new Error(response.error || "Failed to create connection");
      }
    } catch (error: any) {
      const errorMsg =
        error.response?.data?.error ||
        error.message ||
        "Failed to create connection";
      setError(errorMsg);
      toast.error(errorMsg, { id: "greencheck-create" });
    } finally {
      setLoading(false);
    }
  };

  const confirmConnection = () => {
    if (createdCrb || existingConnection) {
      const crbInfo = createdCrb || existingConnection;
      onConnectionSuccess(crbInfo.id, {
        id: crbInfo.id,
        name: crbInfo.name || `${selectedPos?.name} Location`,
        pos_system: selectedPos?.id,
        ...crbInfo,
      });
    }
  };

  const handleCredentialChange = (fieldName: string, value: string) => {
    setCredentials((prev) => ({
      ...prev,
      [fieldName]: value,
    }));
  };

  if (step === "authenticating") {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3EDC81] mx-auto mb-4"></div>
        <h3 className="text-lg font-medium mb-2">Connecting to Green Check</h3>
        <p className="text-gray-400">
          Establishing secure connection to cannabis data platform...
        </p>
      </div>
    );
  }

  if (step === "selecting-pos") {
    return (
      <div>
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">Select Your POS System</h3>
          <p className="text-gray-400 mb-3">
            Green Check is the "Plaid" of POS connections - one secure
            integration that connects to multiple systems.
          </p>
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
            <div className="flex items-center">
              <svg
                className="w-4 h-4 text-green-600 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="text-sm text-green-800 font-medium">
                Unified platform supporting multiple POS systems
              </span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {AVAILABLE_POS_SYSTEMS.map((pos) => (
            <div
              key={pos.id}
              className="border border-border rounded-lg p-4 hover:border-[#3EDC81] transition-colors cursor-pointer"
              onClick={() => checkExistingConnection(pos)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">{pos.name}</h4>
                  <p className="text-sm text-gray-400 mt-1">
                    {pos.description}
                  </p>
                  {pos.manual_intervention_required && (
                    <span className="inline-block mt-2 text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded">
                      Manual Setup Required
                    </span>
                  )}
                </div>
                <div className="text-[#3EDC81]">
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-between">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-400 hover:text-gray-200 transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    );
  }

  if (step === "checking-connection") {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3EDC81] mx-auto mb-4"></div>
        <h3 className="text-lg font-medium mb-2">
          Checking {selectedPos?.name} Connection
        </h3>
        <p className="text-gray-400">
          Verifying your existing connection status...
        </p>
      </div>
    );
  }

  if (step === "collecting-credentials") {
    return (
      <div>
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">
            Connect to {selectedPos?.name}
          </h3>

          {selectedPos?.manual_intervention_required ? (
            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mb-4">
              <div className="flex items-start">
                <svg
                  className="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                <div>
                  <h4 className="text-yellow-400 font-medium mb-2">
                    Manual Setup Required
                  </h4>
                  <p className="text-sm text-gray-300 mb-3">
                    {selectedPos?.name} requires manual intervention to complete
                    the integration. Please contact your {selectedPos?.name}{" "}
                    provider with this message:
                  </p>
                  <div className="bg-surface border border-border rounded p-3 mb-3">
                    <p className="text-sm font-mono">
                      "Hi, I would like to connect my {selectedPos?.name} system
                      to Green Check for data aggregation. Please enable API
                      access for my account and provide the necessary
                      credentials. See the integration documentation for
                      details."
                    </p>
                  </div>
                  {selectedPos?.integration_docs_url && (
                    <a
                      href={selectedPos.integration_docs_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[#3EDC81] text-sm hover:underline"
                    >
                      View Integration Documentation →
                    </a>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div>
              <p className="text-gray-400 mb-4">
                Enter your {selectedPos?.name} credentials to establish the
                connection.
              </p>

              <div className="space-y-4 mb-6">
                {credentialFields.map((field) => (
                  <div key={field.name}>
                    <label className="block text-sm font-medium mb-2">
                      {field.label}
                      {field.required && (
                        <span className="text-red-400 ml-1">*</span>
                      )}
                    </label>
                    <input
                      type={field.type}
                      value={credentials[field.name] || ""}
                      onChange={(e) =>
                        handleCredentialChange(field.name, e.target.value)
                      }
                      className="w-full px-3 py-2 bg-surface border border-border rounded-lg focus:border-[#3EDC81] focus:outline-none"
                      placeholder={field.description}
                      required={field.required}
                    />
                    {field.description && (
                      <p className="text-xs text-gray-500 mt-1">
                        {field.description}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-between">
          <button
            onClick={() => setStep("selecting-pos")}
            className="px-4 py-2 text-gray-400 hover:text-gray-200 transition-colors"
          >
            Back
          </button>

          {!selectedPos?.manual_intervention_required && (
            <button
              onClick={createCrb}
              disabled={
                loading ||
                credentialFields.some(
                  (field) => field.required && !credentials[field.name]
                )
              }
              className="px-6 py-2 bg-[#3EDC81] text-black rounded-lg hover:bg-[#3EDC81]/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? "Connecting..." : "Connect to " + selectedPos?.name}
            </button>
          )}

          {selectedPos?.manual_intervention_required && (
            <button
              onClick={onCancel}
              className="px-6 py-2 bg-surface border border-border rounded-lg hover:bg-surface-secondary transition-colors"
            >
              I'll Contact Them Later
            </button>
          )}
        </div>
      </div>
    );
  }

  if (step === "creating-crb") {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3EDC81] mx-auto mb-4"></div>
        <h3 className="text-lg font-medium mb-2">Creating Connection</h3>
        <p className="text-gray-400">
          Setting up your {selectedPos?.name} connection through Green Check...
        </p>
      </div>
    );
  }

  if (step === "completed") {
    const crbInfo = createdCrb || existingConnection;

    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-[#3EDC81]/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg
            className="w-8 h-8 text-[#3EDC81]"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium mb-2">Successfully Connected!</h3>
        <p className="text-gray-400 mb-6">
          Your {selectedPos?.name} system is now connected through Green Check.
        </p>

        <div className="bg-surface border border-border rounded-lg p-4 mb-6 text-left">
          <h4 className="font-medium mb-2">Connection Details:</h4>
          <div className="text-sm text-gray-400 space-y-1">
            <p>
              <span className="text-gray-300">POS System:</span>{" "}
              {selectedPos?.name}
            </p>
            <p>
              <span className="text-gray-300">CRB ID:</span> {crbInfo?.id}
            </p>
            <p>
              <span className="text-gray-300">Status:</span>{" "}
              <span className="text-[#3EDC81]">Connected</span>
            </p>
          </div>
        </div>

        <button
          onClick={confirmConnection}
          className="px-6 py-2 bg-[#3EDC81] text-black rounded-lg hover:bg-[#3EDC81]/90 transition-colors"
        >
          Continue with This Connection
        </button>
      </div>
    );
  }

  return null;
}
