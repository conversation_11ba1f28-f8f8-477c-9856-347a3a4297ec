import React, { useState } from "react";
import Button from "../../ui/Button";

interface DutchieIntegrationProps {
  onConnectionSuccess: (apiKey: string, locationInfo: any) => void;
  onConnectionError: (error: string) => void;
  onCancel: () => void;
}

const DutchieIntegration: React.FC<DutchieIntegrationProps> = ({
  onConnectionSuccess,
  onConnectionError,
  onCancel,
}) => {
  const [apiKey, setApiKey] = useState("");
  const [consumerKey, setConsumerKey] = useState("");
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState("");

  const handleConnect = async () => {
    if (!apiKey.trim()) {
      setError("API Key is required");
      return;
    }

    setIsConnecting(true);
    setError("");

    try {
      // Test the connection by calling the Dutchie API
      const response = await fetch("https://api.pos.dutchie.com/whoami", {
        method: "GET",
        headers: {
          Authorization: apiKey,
          "Content-Type": "application/json",
          ...(consumerKey && { ConsumerKey: consumerKey }),
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const locationInfo = await response.json();

      // Validate that we got the expected location data
      if (!locationInfo.locationId || !locationInfo.locationName) {
        throw new Error(
          "Invalid response from Dutchie API - missing location information"
        );
      }

      onConnectionSuccess(apiKey, locationInfo);
    } catch (error) {
      let errorMessage = "Failed to connect to Dutchie";

      if (error instanceof Error) {
        if (error.message.includes("401")) {
          errorMessage = "Invalid API Key - please check your credentials";
        } else if (error.message.includes("403")) {
          errorMessage = "Access denied - please verify your API permissions";
        } else if (error.message.includes("Network")) {
          errorMessage =
            "Network error - please check your internet connection";
        } else {
          errorMessage = error.message;
        }
      }

      setError(errorMessage);
      onConnectionError(errorMessage);
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-blue-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Connect to Dutchie POS
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                To connect your Dutchie POS system, you'll need your API
                credentials from your Dutchie account.
              </p>
              <p className="mt-1">
                You can find these in your Dutchie dashboard under Settings →
                API Access.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <label
            htmlFor="apiKey"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            API Key *
          </label>
          <input
            type="password"
            id="apiKey"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            placeholder="Enter your Dutchie API Key"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
            disabled={isConnecting}
          />
        </div>

        <div>
          <label
            htmlFor="consumerKey"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Consumer Key (Optional)
          </label>
          <input
            type="text"
            id="consumerKey"
            value={consumerKey}
            onChange={(e) => setConsumerKey(e.target.value)}
            placeholder="Enter your Consumer Key (if applicable)"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
            disabled={isConnecting}
          />
          <p className="mt-1 text-xs text-gray-500">
            Some integrations may require a Consumer Key for idempotency
            tracking
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Connection Failed
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            onClick={onCancel}
            variant="secondary"
            disabled={isConnecting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConnect}
            variant="primary"
            disabled={isConnecting || !apiKey.trim()}
          >
            {isConnecting ? (
              <div className="flex items-center">
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Connecting...
              </div>
            ) : (
              "Connect to Dutchie"
            )}
          </Button>
        </div>
      </div>

      <div className="text-xs text-gray-500 space-y-1">
        <p>• Your API credentials are encrypted and stored securely</p>
        <p>• We only access data necessary for analytics and reporting</p>
        <p>• You can disconnect at any time from your dashboard</p>
      </div>
    </div>
  );
};

export default DutchieIntegration;
