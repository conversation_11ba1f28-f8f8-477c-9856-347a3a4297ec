.image-upload-with-drag-drop {
  width: 100%;
}

.image-upload-header {
  margin-bottom: 12px;
}

.image-upload-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.hidden-file-input {
  display: none;
}

.image-drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  background-color: #fafafa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-drop-zone:hover:not(.disabled) {
  border-color: #9ca3af;
  background-color: #f3f4f6;
}

.image-drop-zone.drag-over {
  border-color: #3b82f6;
  background-color: #eff6ff;
  border-style: solid;
}

.image-drop-zone.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f9fafb;
}

.image-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #374151;
}

.upload-subtext {
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .image-drop-zone {
    min-height: 150px;
    padding: 16px;
  }
  
  .upload-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }
  
  .upload-text {
    font-size: 14px;
  }
  
  .upload-subtext {
    font-size: 12px;
  }
}
