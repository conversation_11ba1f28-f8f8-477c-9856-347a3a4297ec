import React, { useState } from "react";
import Modal from "../ui/Modal";
import Button from "../ui/Button";
import { useTranslation } from "react-i18next";

interface PasswordVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onVerify: (password: string) => Promise<boolean>;
  title?: string;
  description?: string;
  isVerifying?: boolean;
}

export default function PasswordVerificationModal({
  isOpen,
  onClose,
  onVerify,
  title = "Password Required",
  description = "Please enter the password to continue.",
  isVerifying = false,
}: PasswordVerificationModalProps) {
  const { t } = useTranslation();
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!password.trim()) {
      setError("Password is required");
      return;
    }

    try {
      const isValid = await onVerify(password);
      if (isValid) {
        setPassword("");
        setError("");
        onClose();
      } else {
        setError("Invalid password. Please try again.");
      }
    } catch (error) {
      setError("An error occurred. Please try again.");
    }
  };

  const handleClose = () => {
    if (!isVerifying) {
      setPassword("");
      setError("");
      onClose();
    }
  };

  return (
    <Modal
      title={title}
      open={isOpen}
      onClose={handleClose}
    >
      <div className="modal-content">
        <div className="info-message mb-4">
          <p>{description}</p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="password" className="block text-sm font-medium mb-2">
              Password
            </label>
            <input
              type="password"
              id="password"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isVerifying}
              autoFocus
              placeholder="Enter password"
            />
            {error && (
              <div className="mt-2">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}
          </div>

          <div className="modal-actions">
            <Button
              variant="secondary"
              onClick={handleClose}
              disabled={isVerifying}
            >
              {t("cancel")}
            </Button>
            <Button
              type="submit"
              disabled={isVerifying || !password.trim()}
            >
              {isVerifying ? "Verifying..." : "Continue"}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
