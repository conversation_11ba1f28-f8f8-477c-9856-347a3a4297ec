import React, { useRef, useState } from "react";
import Button from "../ui/Button";
import "./ImageUploadWithDragDrop.css";

interface ImageUploadWithDragDropProps {
  onImageChange: (file: File | null) => void;
  onClear: () => void;
  imagePreview?: string | null;
  imageFile?: File | null;
  disabled?: boolean;
  accept?: string;
  label?: string;
  placeholder?: string;
  className?: string;
}

const ImageUploadWithDragDrop: React.FC<ImageUploadWithDragDropProps> = ({
  onImageChange,
  onClear,
  imagePreview,
  imageFile,
  disabled = false,
  accept = "image/*",
  label = "Image",
  placeholder = "No image selected",
  className = "",
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileSelect = (file: File) => {
    if (file && file.type.startsWith("image/")) {
      onImageChange(file);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleButtonClick = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  return (
    <div className={`image-upload-with-drag-drop ${className}`}>
      <div className="image-upload-header">
        <div className="image-upload-buttons">
          <input
            ref={fileInputRef}
            type="file"
            accept={accept}
            className="hidden-file-input"
            onChange={handleInputChange}
            disabled={disabled}
          />
          <Button
            type="button"
            onClick={handleButtonClick}
            size="small"
            disabled={disabled}
          >
            Select {label}
          </Button>
          {(imagePreview || imageFile) && (
            <Button
              type="button"
              onClick={onClear}
              variant="secondary"
              size="small"
              disabled={disabled}
            >
              Clear
            </Button>
          )}
        </div>
      </div>

      <div
        className={`image-drop-zone ${isDragOver ? "drag-over" : ""} ${
          disabled ? "disabled" : ""
        }`}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        {imagePreview ? (
          <div className="image-preview-container">
            <img
              src={imagePreview}
              alt="Preview"
              className="image-preview"
            />
          </div>
        ) : (
          <div className="image-placeholder">
            <div className="upload-icon">📁</div>
            <p className="upload-text">
              {isDragOver
                ? `Drop ${label.toLowerCase()} here`
                : `Drag & drop ${label.toLowerCase()} here or click to select`}
            </p>
            <p className="upload-subtext">{placeholder}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageUploadWithDragDrop;
