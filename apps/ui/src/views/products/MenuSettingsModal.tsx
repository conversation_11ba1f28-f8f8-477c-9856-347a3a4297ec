import React, { useState, useEffect, useCallback } from "react";
import Modal from "../../ui/Modal";
import Button from "../../ui/Button";
import TextInput from "../../ui/form/TextInput";
import { SingleSelect } from "../../ui/form/SingleSelect";
import CheckboxInput from "../../ui/form/CheckboxInput";
import ImageUploadWithDragDrop from "../../components/ImageUploadWithDragDrop";
import {
  PlusIcon,
  EditIcon,
  TrashIcon,
  SparklesIcon,
  GripVerticalIcon,
  SettingsIcon,
} from "../../ui/icons";
import { toast } from "react-hot-toast";
import { MenuSetting, menuSettingsApi } from "../../api";
import Spinner from "../../ui/Spinner";

interface MenuSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  locationId: number;
}

const DUMMY_CATEGORIES = [
  { name: "Concentrate", icon: "💎" },
  { name: "Edible", icon: "🍫" },
  { name: "Flower", icon: "🌸" },
  { name: "Other", icon: "🛍️" },
  { name: "Pre-roll", icon: "🚬" },
  { name: "Tincture", icon: "💧" },
  { name: "Topical", icon: "🧴" },
];

const DUMMY_PRODUCTS = [
  {
    name: "Labs Ghost Og",
    brand: "Legacy",
    thc: 75,
    price: 10,
    image: "https://via.placeholder.com/150",
  },
  {
    name: "Amaretto Mints",
    brand: "Premium Brand",
    thc: 18,
    price: 25,
    image: "https://via.placeholder.com/150",
  },
  {
    name: "Blaze Key Lime Pie",
    brand: "CALI",
    thc: 18,
    price: 8,
    image: "https://via.placeholder.com/150",
  },
];

// In-file MenuPreview component
const MenuPreview: React.FC<{
  settings: MenuSetting[];
  activeSetting?: Partial<MenuSetting> | null;
}> = ({ settings, activeSetting }) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const getSettingValue = (setting: MenuSetting, key: keyof MenuSetting) => {
    if (activeSetting && setting.id === (activeSetting as MenuSetting).id) {
      return activeSetting[key] ?? setting[key];
    }
    return setting[key];
  };

  const previewSettings = settings.map((s) => {
    if (activeSetting && s.id === (activeSetting as MenuSetting).id) {
      return { ...s, ...activeSetting };
    }
    if (
      activeSetting &&
      !(activeSetting as MenuSetting).id &&
      s.order === activeSetting.order &&
      s.type === activeSetting.type
    ) {
      return { ...s, ...activeSetting };
    }
    return s;
  });

  if (
    activeSetting &&
    !(activeSetting as MenuSetting).id &&
    !previewSettings.some(
      (s) => s.order === activeSetting.order && s.type === activeSetting.type
    )
  ) {
    previewSettings.push(activeSetting as MenuSetting);
  }

  const carouselSlides = previewSettings
    .filter((s) => s.type === "carousel_slide" && s.active)
    .sort((a, b) => (a.order || 0) - (b.order || 0));

  const promotions = previewSettings
    .filter((s) => s.type === "promo_content" && s.active)
    .sort((a, b) => (a.order || 0) - (b.order || 0));

  const announcements = previewSettings
    .filter((s) => s.type === "announcement" && s.active)
    .sort((a, b) => (a.order || 0) - (b.order || 0));

  useEffect(() => {
    if (carouselSlides.length > 1) {
      const timer = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % carouselSlides.length);
      }, 5000);
      return () => clearInterval(timer);
    }
  }, [carouselSlides.length]);

  return (
    <div className="bg-white rounded-lg shadow-xl h-full w-full overflow-y-auto p-2 border border-gray-200 text-sm">
      <div className="w-full max-w-4xl mx-auto">
        {/* Header */}
        <div className="p-3 border-b flex justify-between items-center">
          <div className="text-xl font-bold text-green-600">Menu</div>
          <div className="flex-1 max-w-xs mx-3">
            <input
              type="text"
              placeholder="Search for products..."
              className="w-full px-3 py-1.5 border rounded-full text-xs"
              disabled
            />
          </div>
          <div className="flex items-center gap-3 text-xs">
            <span>Account</span>
            <span>Cart</span>
          </div>
        </div>

        {/* Main Content */}
        <div className="p-3 space-y-6">
          {/* Carousel */}
          {carouselSlides.length > 0 && (
            <div className="relative w-full h-48 bg-gray-200 rounded-lg overflow-hidden shadow-lg">
              {carouselSlides.map((slide, index) => (
                <div
                  key={slide.id || `new-${slide.order}`}
                  className={`absolute w-full h-full transition-opacity duration-500 ${
                    index === currentSlide ? "opacity-100" : "opacity-0"
                  }`}
                >
                  <img
                    src={
                      slide.image_url || "https://via.placeholder.com/800x400"
                    }
                    alt={slide.title || "Carousel image"}
                    className="w-full h-full object-cover"
                  />
                  {/* <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center p-4">
                    <h2 className="text-white text-2xl font-bold text-center">
                      {slide.title}
                    </h2>
                  </div> */}
                </div>
              ))}
              {carouselSlides.length > 1 && (
                <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-2">
                  {carouselSlides.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className={`w-2 h-2 rounded-full ${
                        index === currentSlide ? "bg-white" : "bg-gray-400"
                      }`}
                    ></button>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Promotions */}
          {promotions.map((promo) => (
            <div
              key={promo.id || `new-${promo.order}`}
              className="bg-rose-50 border border-rose-200 rounded-lg p-3"
            >
              <h3 className="font-bold text-rose-800 text-base">
                {promo.title}
              </h3>
              <p className="text-xs text-rose-700 mt-1">
                {promo.description}{" "}
                {promo.link && (
                  <a
                    href="#"
                    onClick={(e) => e.preventDefault()}
                    className="underline font-semibold"
                  >
                    Learn More
                  </a>
                )}
              </p>
            </div>
          ))}

          {/* Announcements */}
          {announcements.map((item) => (
            <div key={item.id || `new-${item.order}`}>
              <h3 className="font-semibold text-base text-black">
                Announcements
              </h3>
              <div className="mt-2 bg-gray-50 border border-gray-200 rounded-lg p-3 text-black">
                <span className="font-bold text-sm">{item.title}</span>
                <p className="text-xs text-gray-600">{item.description}</p>
              </div>
            </div>
          ))}

          {/* Categories */}
          <div>
            <h3 className="font-semibold text-base text-black">Categories</h3>
            <div className="grid grid-cols-7 gap-2 mt-2">
              {DUMMY_CATEGORIES.map((cat) => (
                <div
                  key={cat.name}
                  className="flex flex-col items-center gap-1 p-1 border rounded-lg shadow-sm overflow-hidden"
                >
                  <div className="text-2xl">{cat.icon}</div>
                  <span className="text-xs font-medium text-center text-black overflow-hidden text-ellipsis whitespace-nowrap">
                    {cat.name}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Popular Flower */}
          <div>
            <div className="flex justify-between items-center">
              <h3 className="font-semibold text-base">Popular Flower</h3>
              <a href="#" className="text-xs font-medium text-green-600">
                View All →
              </a>
            </div>
            <div className="grid grid-cols-3 gap-3 mt-2">
              {DUMMY_PRODUCTS.map((prod) => (
                <div
                  key={prod.name}
                  className="border rounded-lg p-2 shadow-sm"
                >
                  <div className="w-full h-24 bg-gray-100 rounded-md mb-2"></div>
                  <span className="font-semibold truncate text-sm text-black">
                    {prod.name}
                  </span>
                  <p className="text-xs text-gray-500">{prod.brand}</p>
                  <p className="text-xs text-gray-700 mt-1">THC: {prod.thc}%</p>
                  <div className="flex justify-between items-center mt-2">
                    <span className="font-bold text-base text-black">
                      ${prod.price}
                    </span>
                    <button className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-lg">
                      +
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const MenuSettingsModal: React.FC<MenuSettingsModalProps> = ({
  isOpen,
  onClose,
  locationId,
}) => {
  const [settings, setSettings] = useState<MenuSetting[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingSetting, setEditingSetting] = useState<MenuSetting | null>(
    null
  );
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<Partial<MenuSetting>>({});
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("content");
  const [draggedItem, setDraggedItem] = useState<MenuSetting | null>(null);
  const [dragOverItem, setDragOverItem] = useState<MenuSetting | null>(null);

  const menuTypes = [
    { value: "carousel_slide", label: "Carousel Slide" },
    { value: "promo_content", label: "Promotion" },
    { value: "announcement", label: "Announcement" },
  ];

  const fetchSettings = useCallback(async () => {
    try {
      setLoading(true);
      const result = await menuSettingsApi.search(locationId, { limit: 100 });
      setSettings(result.results || []);
    } catch (err) {
      console.error(err);
      toast.error("Failed to load menu settings");
      setSettings([]);
    } finally {
      setLoading(false);
    }
  }, [locationId]);

  useEffect(() => {
    if (isOpen) {
      fetchSettings();
    }
  }, [isOpen, fetchSettings]);

  useEffect(() => {
    if (formData.image_url) {
      setImagePreview(formData.image_url);
    }
  }, [formData.image_url]);

  const handleImageChange = (file: File | null) => {
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        setImagePreview(result);
        setFormData((prev) => ({ ...prev, image_url: "" }));
      };
      reader.readAsDataURL(file);
    }
  };

  const clearImageFields = () => {
    setImageFile(null);
    setImagePreview(null);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingSetting(null);
    setFormData({});
    clearImageFields();
  };

  const handleAddNew = () => {
    setEditingSetting(null);
    const maxOrder =
      settings.length > 0 ? Math.max(...settings.map((s) => s.order || 0)) : 0;
    setFormData({
      type: "carousel_slide",
      title: "",
      description: "",
      image_url: "",
      link: "",
      order: maxOrder + 1,
      active: true,
    });
    setShowForm(true);
    clearImageFields();
  };

  const handleEdit = (setting: MenuSetting) => {
    setEditingSetting(setting);
    setFormData({ ...setting });
    setShowForm(true);
    clearImageFields();
    if (setting.image_url) {
      setImagePreview(setting.image_url);
    }
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, setting: MenuSetting) => {
    setDraggedItem(setting);
    e.dataTransfer.effectAllowed = "move";
  };

  const handleDragOver = (e: React.DragEvent, setting: MenuSetting) => {
    e.preventDefault();
    setDragOverItem(setting);
    e.dataTransfer.dropEffect = "move";
  };

  const handleDragLeave = () => {
    setDragOverItem(null);
  };

  const handleDrop = async (e: React.DragEvent, targetSetting: MenuSetting) => {
    e.preventDefault();

    if (!draggedItem || draggedItem.id === targetSetting.id) {
      setDraggedItem(null);
      setDragOverItem(null);
      return;
    }

    // Only allow reordering within the same type
    if (draggedItem.type !== targetSetting.type) {
      toast.error("Items can only be reordered within the same type");
      setDraggedItem(null);
      setDragOverItem(null);
      return;
    }

    try {
      setLoading(true);

      // Get all settings of the same type, sorted by order
      const sameTypeSettings = settings
        .filter((s) => s.type === draggedItem.type)
        .sort((a, b) => (a.order || 0) - (b.order || 0));

      // Remove the dragged item from the list
      const withoutDragged = sameTypeSettings.filter(
        (s) => s.id !== draggedItem.id
      );

      // Find the target index
      const targetIndex = withoutDragged.findIndex(
        (s) => s.id === targetSetting.id
      );

      // Insert the dragged item at the target position
      const newOrder = [...withoutDragged];
      newOrder.splice(targetIndex, 0, draggedItem);

      // Update the order values (1-based)
      const updatePromises = newOrder.map((setting, index) => {
        const newOrderValue = index + 1;
        if (setting.order !== newOrderValue) {
          return menuSettingsApi.update(locationId, setting.id, {
            location_id: setting.location_id,
            type: setting.type,
            title: setting.title,
            description: setting.description,
            image_url: setting.image_url,
            link: setting.link,
            order: newOrderValue,
            active: setting.active,
            start_date: setting.start_date,
            end_date: setting.end_date,
            metadata: setting.metadata,
          });
        }
        return Promise.resolve();
      });

      await Promise.all(updatePromises);

      // Refresh the settings
      await fetchSettings();
      toast.success("Order updated successfully");
    } catch (error) {
      console.error("Error updating order:", error);
      toast.error("Failed to update order");
    } finally {
      setLoading(false);
      setDraggedItem(null);
      setDragOverItem(null);
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      // Clean and prepare the data
      const cleanFormData: any = {
        type:
          typeof formData.type === "object"
            ? (formData.type as any).value
            : formData.type || "carousel_slide",
        title: formData.title || "",
        description: formData.description || "",
        link: formData.link || "",
        order: formData.order || 0,
        active: formData.active !== false, // Default to true
        start_date: formData.start_date || undefined,
        end_date: formData.end_date || undefined,
        metadata: formData.metadata || undefined,
      };

      // Handle image URL
      if (formData.image_url && formData.image_url.trim() !== "") {
        cleanFormData.download_image_url = formData.image_url;
      }

      if (imageFile) {
        const formDataToSend = new FormData();
        Object.entries(cleanFormData).forEach(([key, value]) => {
          // Don't send image_url when uploading a file, and skip undefined values
          if (value !== undefined && key !== "download_image_url") {
            formDataToSend.append(key, String(value));
          }
        });
        formDataToSend.append("menuImage", imageFile);

        // Use raw fetch for multipart/form-data
        const url = editingSetting
          ? `/api/admin/locations/${locationId}/menu-settings/${editingSetting.id}`
          : `/api/admin/locations/${locationId}/menu-settings`;
        const method = editingSetting ? "PUT" : "POST";

        const response = await fetch(url, {
          method,
          body: formDataToSend,
          headers: { Accept: "application/json" },
          credentials: "include",
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.error || `Request failed: ${response.statusText}`
          );
        }
      } else {
        // Use the API wrapper for application/json payloads
        const dataToSend = { ...cleanFormData };

        if (editingSetting) {
          await menuSettingsApi.update(
            locationId,
            editingSetting.id,
            dataToSend as any
          );
        } else {
          await menuSettingsApi.create(locationId, dataToSend as any);
        }
      }

      toast.success(
        `Menu setting ${editingSetting ? "updated" : "created"} successfully`
      );
      await fetchSettings();
      handleCloseForm();
    } catch (err) {
      console.error(err);
      toast.error("Failed to save menu setting");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!window.confirm("Are you sure you want to delete this menu setting?"))
      return;
    try {
      setLoading(true);
      await menuSettingsApi.delete(locationId, id);
      toast.success("Menu setting deleted successfully");
      await fetchSettings();
    } catch (err) {
      console.error(err);
      toast.error("Failed to delete menu setting");
    } finally {
      setLoading(false);
    }
  };

  const sortedSettings = [...settings].sort((a, b) => {
    // First sort by type
    if (a.type < b.type) return -1;
    if (a.type > b.type) return 1;
    // Then by order within the same type
    return (a.order || 0) - (b.order || 0);
  });

  // Group settings by type for better display
  const groupedSettings = sortedSettings.reduce((groups, setting) => {
    const type = setting.type;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(setting);
    return groups;
  }, {} as Record<string, MenuSetting[]>);

  return (
    <Modal
      title="Customize Menu"
      open={isOpen}
      onClose={onClose}
      size="fullscreen"
    >
      <div className="flex h-full w-full">
        {/* Left Panel */}
        <div className="w-1/3 p-6 space-y-4 border-r overflow-y-auto">
          <div className="flex border-b">
            <button
              onClick={() => setActiveTab("content")}
              className={`px-4 py-2 text-sm font-medium ${
                activeTab === "content"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Content
            </button>
            <button
              onClick={() => setActiveTab("appearance")}
              className={`px-4 py-2 text-sm font-medium ${
                activeTab === "appearance"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Appearance
            </button>
          </div>

          {activeTab === "content" && !showForm && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <Button icon={<PlusIcon />} onClick={handleAddNew}>
                  Add New
                </Button>
              </div>
              <div className="space-y-4">
                {loading ? (
                  <Spinner />
                ) : Object.keys(groupedSettings).length > 0 ? (
                  Object.entries(groupedSettings).map(
                    ([type, typeSettings]) => (
                      <div key={type} className="space-y-2">
                        <h4 className="text-sm font-semibold text-gray-700 capitalize border-b border-gray-200 pb-1">
                          {type.replace(/_/g, " ")}s ({typeSettings.length})
                        </h4>
                        <div className="space-y-2">
                          {typeSettings.map((setting) => (
                            <div
                              key={setting.id}
                              className={`border rounded-lg p-3 shadow-sm cursor-move ${
                                dragOverItem?.id === setting.id
                                  ? "bg-blue-50 border-blue-300"
                                  : ""
                              }`}
                              draggable
                              onDragStart={(e) => handleDragStart(e, setting)}
                              onDragOver={(e) => handleDragOver(e, setting)}
                              onDragLeave={handleDragLeave}
                              onDrop={(e) => handleDrop(e, setting)}
                            >
                              <div className="flex justify-between items-start">
                                <div className="flex items-start gap-2 flex-1">
                                  <div
                                    style={{
                                      cursor: "move",
                                      height: "100%",
                                      display: "flex",
                                      alignItems: "center",
                                      justifyContent: "center",
                                      width: "30px",
                                    }}
                                  >
                                    <GripVerticalIcon className="text-gray-400 flex-shrink-0" />
                                  </div>
                                  <div className="flex-1 flex items-center">
                                    <span className="font-semibold text-sm">
                                      {setting.title}
                                    </span>
                                  </div>
                                </div>
                                <div className="flex gap-2">
                                  <Button
                                    variant="secondary"
                                    size="small"
                                    icon={<EditIcon />}
                                    onClick={() => handleEdit(setting)}
                                  />
                                  <Button
                                    variant="destructive"
                                    size="small"
                                    icon={<TrashIcon />}
                                    onClick={() => handleDelete(setting.id)}
                                  />
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )
                  )
                ) : (
                  <div className="text-center py-12">
                    <div className="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                      <div className="flex items-center justify-center w-6 h-6 text-gray-400">
                        <SettingsIcon />
                      </div>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No menu content yet
                    </h3>
                    <p className="text-sm text-gray-500 mb-4">
                      Get started by adding announcements, promotions, or
                      carousel slides to customize your menu.
                    </p>
                    <Button
                      icon={<PlusIcon />}
                      onClick={handleAddNew}
                      size="small"
                    >
                      Add Your First Item
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === "content" && showForm && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">
                {editingSetting ? "Edit Content" : "Add New Content"}
              </h3>
              <SingleSelect
                label="Type"
                value={formData.type || "carousel_slide"}
                onChange={(v) => setFormData({ ...formData, type: v })}
                required
                options={menuTypes}
                getOptionDisplay={(option) => option.label}
                toValue={(option) => option.value}
              />
              <TextInput
                name="title"
                label="Title"
                value={formData.title || ""}
                onChange={(v) => setFormData({ ...formData, title: v })}
                required
              />
              <textarea
                className="w-full min-h-[80px] p-3 border rounded-md"
                value={formData.description || ""}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                placeholder="Description..."
              />
              <div className="space-y-2">
                <label className="text-sm font-medium">Image</label>
                <ImageUploadWithDragDrop
                  onImageChange={handleImageChange}
                  onClear={() => {
                    clearImageFields();
                    setFormData({ ...formData, image_url: "" });
                  }}
                  imagePreview={imagePreview}
                  imageFile={imageFile}
                  label="Image"
                  placeholder="No image selected"
                />
                <div className="text-sm text-center text-gray-400">or</div>
                <TextInput
                  name="image_url"
                  label="Image URL"
                  value={formData.image_url || ""}
                  onChange={(v) => {
                    setFormData({ ...formData, image_url: v });
                    if (v) clearImageFields();
                  }}
                  disabled={!!imageFile}
                />
              </div>
              <TextInput
                name="link"
                label="Link URL"
                value={formData.link || ""}
                onChange={(v) => setFormData({ ...formData, link: v })}
              />
              <CheckboxInput
                label="Active"
                checked={formData.active ?? true}
                onChange={(v) => setFormData({ ...formData, active: v })}
              />
              <div className="flex justify-end gap-3 pt-4">
                <Button
                  variant="secondary"
                  onClick={handleCloseForm}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button onClick={handleSubmit} disabled={loading}>
                  {loading ? "Saving..." : editingSetting ? "Update" : "Create"}
                </Button>
              </div>
            </div>
          )}

          {activeTab === "appearance" && (
            <div>
              <h3 className="text-lg font-medium">Appearance Settings</h3>
              <div className="mt-4 p-8 text-center border-2 border-dashed rounded-lg">
                <SparklesIcon className="w-8 h-8 mx-auto text-gray-400" />
                <p className="mt-4 text-sm font-medium text-gray-600">
                  Coming Soon!
                </p>
                <p className="mt-1 text-xs text-gray-500">
                  Customize colors, fonts, and layout options for your menu.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Right Panel */}
        <div className="w-2/3 p-4 bg-gray-100">
          <MenuPreview
            settings={settings}
            activeSetting={showForm ? formData : null}
          />
        </div>
      </div>
    </Modal>
  );
};

export default MenuSettingsModal;
