import React, {
  useCallback,
  useState,
  useEffect,
  useContext,
  useRef,
} from "react";
import { createPortal } from "react-dom";
import { useNavigate } from "react-router-dom";
import api from "../../api";
import { LocationContext } from "../../contexts";
import { Product, SearchResult } from "../../types";
import PageContent from "../../ui/PageContent";
import Button from "../../ui/Button";
import {
  UploadIcon,
  RefreshIcon,
  PlusIcon,
  ChevronDownIcon,
  SparklesIcon,
  TrashIcon,
  SettingsIcon,
} from "../../ui/icons";
import { useTranslation } from "react-i18next";
import {
  SearchTable,
  useSearchTableQueryState,
  BulkAction,
} from "../../ui/SearchTable";
import Modal from "../../ui/Modal";
import FormWrapper from "../../ui/form/FormWrapper";
import PasswordVerificationModal from "../../components/PasswordVerificationModal";
import UploadField from "../../ui/form/UploadField";
import UploadInstructions from "../../ui/form/UploadInstructions";
import Spinner from "../../ui/Spinner";
import TextInput from "../../ui/form/TextInput";
import CheckboxInput from "../../ui/form/CheckboxInput";
import { toast, Toaster } from "react-hot-toast";
import MenuSettingsModal from "./MenuSettingsModal";
import AIStatusBadge from "../../components/AIStatusBadge";
import "./Products.css";

// Extend Product type to include enhancement_status
interface EnhancedProduct extends Product {
  enhancement_status?: string;
  thc?: number;
  cbd?: number;
  rating?: number;
  reviews_count?: number;
  is_active?: boolean;
}

// Simple Badge component
interface BadgeProps {
  children: React.ReactNode;
  color?: string;
}

const Badge = ({ children, color = "gray" }: BadgeProps) => {
  return (
    <span
      className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium text-white`}
      style={{ backgroundColor: color }}
    >
      {children}
    </span>
  );
};

// Portal-based Tooltip component to escape z-index stacking contexts
interface TooltipProps {
  children: React.ReactNode;
  content: string;
}

const Tooltip = ({ children, content }: TooltipProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);

  const updatePosition = () => {
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      const tooltipWidth = 280; // Estimated tooltip width
      const tooltipHeight = 60; // Estimated tooltip height
      const padding = 8;

      let x = rect.left + rect.width / 2;
      let y = rect.top - padding;

      // Adjust horizontal position if tooltip would go off screen
      if (x - tooltipWidth / 2 < padding) {
        x = tooltipWidth / 2 + padding;
      } else if (x + tooltipWidth / 2 > window.innerWidth - padding) {
        x = window.innerWidth - tooltipWidth / 2 - padding;
      }

      // Adjust vertical position if tooltip would go off screen
      if (y - tooltipHeight < padding) {
        y = rect.bottom + padding; // Position below instead
      }

      setPosition({ x, y });
    }
  };

  const handleMouseEnter = () => {
    updatePosition();
    setIsVisible(true);
  };

  const handleMouseLeave = () => {
    setIsVisible(false);
  };

  // Create portal content
  const tooltipContent = isVisible && (
    <div
      className="fixed bg-gray-900 text-white text-xs p-3 rounded shadow-lg pointer-events-none"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: "translate(-50%, -100%)",
        zIndex: 9999,
        maxWidth: "280px",
        whiteSpace: "normal",
        wordWrap: "break-word",
        lineHeight: "1.4",
      }}
    >
      {content}
      <div
        className="absolute border-4 border-transparent border-t-gray-900"
        style={{
          left: "50%",
          top: "100%",
          transform: "translateX(-50%)",
        }}
      />
    </div>
  );

  return (
    <>
      <div
        ref={triggerRef}
        className="inline-block"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {children}
      </div>
      {typeof document !== "undefined" &&
        document.body &&
        createPortal(tooltipContent, document.body)}
    </>
  );
};

// VectorizationStatus type definition
type VectorizationStatus =
  | "processing"
  | "completed"
  | "failed"
  | "not_started"
  | "incomplete"
  | "complete"
  | "excess"
  | "no_data";

// Enhancement status definition
type EnhancementStatus =
  | "complete"
  | "failed"
  | "skipped"
  | "pending"
  | "not_started";

// Define the structure for vectorization status response
interface VectorizationStatusResponse {
  // Combined final status
  status: VectorizationStatus;

  // Detailed information
  job_status?: VectorizationStatus;
  job_summary: {
    isProcessing: boolean;
    completed: number;
    failed: number;
    jobs: {
      error?: string;
    }[];
  };

  // Vector statistics
  namespace?: string;
  vector_count?: number;
  db_record_count?: number;
  vector_status?: VectorizationStatus;
  is_fully_indexed?: boolean;
  completion_percentage?: number;

  // Error information
  error?: string;
}

// Define the marketplace sync response interface
interface MarketplaceSyncResponse {
  success: boolean;
  message: string;
  syncedProducts?: number;
  aiEnhanced?: boolean;
  reindexed?: boolean;
  scrapeInitiated?: boolean;
  productCount?: number;
  estimatedScrapeTime?: number;
  status?: string;
}

// Define the product enhancement response interface
interface ProductEnhancementResponse {
  message: string;
  enhanced_count: number;
  vectorized_count: number;
  failed_vectorization: number;
  async?: boolean;
  status?: string;
  count?: number;
}

// Add cost estimation interface
interface CostEstimationResponse {
  success: boolean;
  originalCount: number;
  deduplicatedCount: number;
  estimatedCost: number;
  estimatedTokens: number;
  processingStrategy: {
    expressBatch: number;
    standardBatch: number;
    deepEnhancementBatch: number;
  };
  costPerProduct: number;
  recommendedMode: string;
}

const productFields = [
  {
    name: "meta_sku",
    required: true,
    description: "Unique identifier for the product",
  },
  {
    name: "retailer_id",
    required: true,
    description: "Retailer's product identifier",
  },
  {
    name: "raw_product_name",
    required: true,
    description: "Original product name",
  },
  {
    name: "product_name",
    required: true,
    description: "Standardized product name",
  },
  {
    name: "medical",
    required: true,
    description: "true/false if product is medical",
  },
  {
    name: "recreational",
    required: true,
    description: "true/false if product is recreational",
  },
  { name: "cann_sku_id", description: "Cannabis SKU identifier" },
  { name: "brand_name", description: "Product brand name" },
  { name: "brand_id", description: "Brand identifier (number)" },
  { name: "url", description: "Product URL" },
  { name: "image_url", description: "Product image URL" },
  { name: "raw_weight_string", description: "Original weight string" },
  { name: "display_weight", description: "Formatted weight for display" },
  { name: "raw_product_category", description: "Original product category" },
  { name: "category", description: "Standardized product category" },
  { name: "raw_subcategory", description: "Original product subcategory" },
  { name: "subcategory", description: "Standardized product subcategory" },
  { name: "product_tags", description: "Comma-separated list of tags" },
  { name: "percentage_thc", description: "THC percentage (number)" },
  { name: "percentage_cbd", description: "CBD percentage (number)" },
  { name: "mg_thc", description: "THC content in milligrams (number)" },
  { name: "mg_cbd", description: "CBD content in milligrams (number)" },
  {
    name: "quantity_per_package",
    description: "Number of items per package (number)",
  },
  { name: "latest_price", description: "Current product price (number)" },
  { name: "menu_provider", description: "Menu provider identifier" },
  {
    name: "is_active",
    description: "true/false if product is active (visible)",
  },
];

export default function ProductsPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [location] = useContext(LocationContext);
  const [isUploadOpen, setIsUploadOpen] = useState(false);
  const [isSyncOpen, setIsSyncOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [isEnhanceOpen, setIsEnhanceOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [isVerifyingPassword, setIsVerifyingPassword] = useState(false);
  const [skipScrape, setSkipScrape] = useState(false);
  const [retailerId, setRetailerId] = useState("");
  const [enhanceLimit, setEnhanceLimit] = useState(100);
  const [vectorStatus, setVectorStatus] =
    useState<VectorizationStatusResponse | null>(null);
  const [isVectorizing, setIsVectorizing] = useState(false);
  const [isBulkVectorizing, setIsBulkVectorizing] = useState(false);
  const [isIndexModalOpen, setIsIndexModalOpen] = useState(false);
  const [isMenuSettingsOpen, setIsMenuSettingsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  // Add cost estimation state
  const [costEstimate, setCostEstimate] =
    useState<CostEstimationResponse | null>(null);
  const [isLoadingCostEstimate, setIsLoadingCostEstimate] = useState(false);
  const [costEstimateError, setCostEstimateError] = useState<string | null>(
    null
  );

  console.log({ location });

  const state = useSearchTableQueryState<EnhancedProduct>(
    useCallback(
      async (params) => {
        return await api.products.search(location.id, {
          ...params,
          limit: 25,
          sort: params.sort || "created_at",
          direction: params.direction || "desc",
        });
      },
      [location.id]
    )
  );

  // Update enhanceLimit whenever search results change
  useEffect(() => {
    if (state.results && state.results.total) {
      setEnhanceLimit(state.results.total);
    }
  }, [state.results, state.results?.total]); // Only depend on the total, not the entire results object

  // Load vectorization status
  const fetchVectorizationStatus = useCallback(async () => {
    try {
      const status = await api.products.getVectorizationStatus(location.id);
      setVectorStatus(status);
      if (!status.job_summary.isProcessing && pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    } catch (error) {
      console.error("Error fetching vectorization status:", error);
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    }
  }, [location.id]);

  // Re-vectorize products
  const startReVectorization = async () => {
    if (!location.id) return;

    try {
      setIsVectorizing(true);

      // Immediately update status to show "Studying" for instant feedback
      if (vectorStatus) {
        setVectorStatus({
          ...vectorStatus,
          status: "processing",
          job_summary: {
            ...vectorStatus.job_summary,
            isProcessing: true,
          },
        });
      }

      await api.products.reVectorize(location.id);
      toast.success("Smokey is now retraining!");

      // Refresh status after a short delay
      setTimeout(() => {
        fetchVectorizationStatus();
      }, 2000);
    } catch (error) {
      console.error("Error starting re-vectorization:", error);
      toast.error("Failed to start retraining. Please try again.");
    } finally {
      setIsVectorizing(false);
    }
  };

  // Bulk vectorize enhanced products
  const startBulkVectorization = async () => {
    if (!location.id) return;

    try {
      setIsBulkVectorizing(true);

      // Immediately update status to show "Studying" for instant feedback
      if (vectorStatus) {
        setVectorStatus({
          ...vectorStatus,
          status: "processing",
          job_summary: {
            ...vectorStatus.job_summary,
            isProcessing: true,
          },
        });
      }

      // Call the new bulk vectorization endpoint
      const response = await fetch(
        `/api/admin/locations/${location.id}/products/bulk-vectorize`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to start bulk vectorization");
      }

      const result = await response.json();

      toast.success("Smokey is now studying your products!");

      // Refresh status after a short delay
      setTimeout(() => {
        fetchVectorizationStatus();
      }, 2000);
    } catch (error) {
      console.error("Error starting bulk vectorization:", error);
      toast.error("Failed to start bulk vectorization. Please try again.");
    } finally {
      setIsBulkVectorizing(false);
    }
  };

  // Add cost estimation function
  const fetchCostEstimate = useCallback(
    async (productCount: number) => {
      if (!location.id || productCount <= 0) {
        setCostEstimate(null);
        return;
      }

      try {
        setIsLoadingCostEstimate(true);
        setCostEstimateError(null);

        // Call the cost estimation endpoint with just the count
        // Backend will handle product selection and cost calculation
        const estimateResponse = await fetch(
          `/api/admin/locations/${location.id}/products/optimization/estimate`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              limit: productCount,
              force_mode: "auto",
            }),
            credentials: "include",
          }
        );

        if (!estimateResponse.ok) {
          throw new Error(
            `Cost estimation failed: ${estimateResponse.statusText}`
          );
        }

        const estimateData = await estimateResponse.json();
        setCostEstimate(estimateData);
      } catch (error) {
        console.error("Error fetching cost estimate:", error);
        setCostEstimateError(
          error instanceof Error ? error.message : "Failed to estimate costs"
        );
        setCostEstimate(null);
      } finally {
        setIsLoadingCostEstimate(false);
      }
    },
    [location.id]
  );

  // Enhance products with AI
  const enhanceProducts = async () => {
    if (!location.id) return;

    try {
      setIsEnhancing(true);
      const result = await api.products.enhance(location.id, {
        limit: enhanceLimit,
        async: true, // Enable async processing
      });

      // Check if this is async processing
      if (result.async) {
        toast.success(
          <div>
            <p>
              <strong>Product Enhancement Started!</strong>
            </p>
            <p className="mt-2">
              Your request to enhance {result.count} products has been queued.
            </p>
            <p className="mt-2">
              This process will continue in the background and may take several
              minutes to complete. Enhanced products will be automatically
              vectorized for search when enhancement is finished.
            </p>
            <p className="mt-2 text-sm text-gray-600">
              You can continue using the application while processing is in
              progress.
            </p>
          </div>,
          { duration: 10000 }
        );
      } else {
        // Show immediate success message for synchronous processing
        toast.success(
          <div>
            <p>
              <strong>Product Enhancement Complete!</strong>
            </p>
            <p className="mt-2">
              Successfully enhanced {result.enhanced_count} products.
            </p>
            <p className="mt-1">
              Vectorized {result.vectorized_count} products for search.
            </p>
            {result.failed_vectorization && result.failed_vectorization > 0 && (
              <p className="mt-1 text-orange-600">
                {result.failed_vectorization} products failed vectorization.
              </p>
            )}
          </div>,
          { duration: 5000 }
        );
      }

      // Close modal
      setIsEnhanceOpen(false);
      await state.reload();

      // Refresh vectorization status
      await fetchVectorizationStatus();
    } catch (error) {
      console.error("Error enhancing products:", error);
      toast.error("Failed to enhance products. Please try again.");
    } finally {
      setIsEnhancing(false);
    }
  };

  // Load vectorization status on mount and set up polling
  useEffect(() => {
    fetchVectorizationStatus();

    // Set up base polling every 10 seconds
    const baseInterval = setInterval(() => {
      fetchVectorizationStatus();
    }, 10000);

    return () => {
      clearInterval(baseInterval);
    };
  }, [fetchVectorizationStatus, location.id]);

  const uploadProducts = async (file: FileList) => {
    if (!file[0].name.toLowerCase().endsWith(".csv")) {
      alert(t("only_csv_files_allowed"));
      return;
    }

    const formData = new FormData();
    formData.append("productsData", file[0]);

    await api.products.upload(location.id, formData);
    setIsUploadOpen(false);
    await state.reload();
  };

  // Verify password for marketplace sync
  const verifyMarketplacePassword = async (password: string): Promise<boolean> => {
    try {
      setIsVerifyingPassword(true);
      const result = await api.products.verifyMarketplacePassword(location.id, password);
      return result.valid;
    } catch (error) {
      console.error("Error verifying password:", error);
      return false;
    } finally {
      setIsVerifyingPassword(false);
    }
  };

  // Handle password verification success
  const handlePasswordVerified = () => {
    setIsPasswordModalOpen(false);
    setIsSyncOpen(true);
  };

  // Sync products from marketplace
  const syncFromMarketplace = async () => {
    if (!location || !location.retailer_id) {
      toast.error(
        "This location doesn't have a retailer ID associated with it."
      );
      setIsSyncOpen(false);
      return;
    }

    try {
      setIsSyncing(true);
      const result = (await api.products.syncMarketplace(
        location.id,
        location.retailer_id,
        { skip_scrape: skipScrape }
      )) as MarketplaceSyncResponse;

      // Show different messages based on response
      if (result.status === "processing") {
        const waitTime = result.estimatedScrapeTime || 5;

        toast.success(
          <div>
            <p>
              <strong>Sync process initiated!</strong>
            </p>
            <p className="mt-2">
              We're fetching fresh data for your{" "}
              {result.productCount || "products"}. This will take approximately{" "}
              {waitTime} minutes to complete.
            </p>
            <p className="mt-2">
              The updated products will be imported automatically once the
              scraping process is complete. Check back later for the most
              up-to-date data.
            </p>
          </div>,
          { duration: 8000 }
        );
      } else if (
        result.status === "completed" &&
        result.syncedProducts !== undefined &&
        !result.scrapeInitiated
      ) {
        // Direct DB sync successful
        toast.success(
          <div>
            <p>Successfully synced products directly from the database.</p>
          </div>,
          { duration: 5000 }
        );
      } else if (result.syncedProducts !== undefined) {
        // Fallback sync or immediate sync result where scraping was attempted
        toast.success(
          <div>
            <p>Successfully synced products from the marketplace.</p>
            {result.message && result.message.includes("Scraping failed") && (
              <p className="mt-2 text-orange-600">
                <strong>Warning:</strong> Marketplace scraping failed. Data
                synced is based on the last successful scrape.
              </p>
            )}
            {result.message &&
              result.message.includes("error occurred during scraping") && (
                <p className="mt-2 text-orange-600">
                  <strong>Warning:</strong> An error occurred during scraping.
                  Data synced is based on the last successful scrape.
                </p>
              )}
          </div>,
          { duration: 8000 }
        );
      } else {
        toast.success(result.message || "Sync process completed.");
      }

      // Close modal and reload product list
      setIsSyncOpen(false);
      await state.reload();

      // Refresh vectorization status
      await fetchVectorizationStatus();
    } catch (error) {
      console.error("Error syncing from marketplace:", error);
      toast.error(
        "Failed to sync products from marketplace. Please try again."
      );
    } finally {
      setIsSyncing(false);
    }
  };

  const downloadExample = () => {
    const headers = productFields.map((f) => f.name).join(",");
    const example =
      headers +
      "\n" +
      'SKU123,RET456,Raw Blue Dream,Blue Dream,true,false,CBD123,Top Brand,1001,https://example.com/product,https://example.com/image.jpg,"3.5g","3.5g",Raw Flower,Flower,Raw Indica,Indica,"premium,popular",24.5,0.5,245,5,1,29.99,provider1';

    const blob = new Blob([example], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "products_example.csv";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  // Get status badge color based on status
  const getStatusColor = (status: VectorizationStatus | EnhancementStatus) => {
    switch (status) {
      case "processing":
      case "pending":
        return "blue";
      case "completed":
      case "complete":
        return "green";
      case "failed":
        return "red";
      case "incomplete":
      case "skipped":
        return "orange";
      case "excess":
        return "purple";
      default:
        return "gray";
    }
  };

  const getSmokeyStatus = (
    status: VectorizationStatusResponse | null | undefined
  ) => {
    if (!status) {
      return {
        color: "#6b7280", // gray
        text: "Unknown",
        description:
          "Smokey is waiting for his first lesson. Let's get him trained up!",
        subtext: "Status is currently unavailable.",
        progressColor: "bg-gray-400",
      };
    }

    const percentage = status.completion_percentage ?? 0;

    switch (status.status) {
      case "completed":
      case "complete":
        return {
          color: "#10b981", // green
          text: "Ready",
          description: "Smokey's knowledge is up-to-date.",
          subtext:
            "Smokey is fully trained and ready to engage with customers.",
          progressColor: "bg-green-500",
        };
      case "processing":
        return {
          color: "#3b82f6", // blue
          text: "Studying",
          description: "Smokey is in a training session.",
          subtext: `A training job is in progress... Status will update automatically.`,
          progressColor: "bg-blue-500",
        };
      case "incomplete":
        return {
          color: "#f59e0b", // orange
          text: "Needs Update",
          description: `Smokey's knowledge is a bit behind. `,
          subtext: `His knowledge is ${percentage}% complete. A quick retrain will catch him up on your latest products.`,
          progressColor: "bg-yellow-500",
        };
      case "excess":
        return {
          color: "#ef4444", // red
          text: "Needs Retrain",
          description: `Smokey has outdated knowledge.`,
          subtext: `Knowledge base is at ${percentage}%. A retrain will bring it back to 100%.`,
          progressColor: "bg-red-500",
        };
      case "failed":
        return {
          color: "#ef4444", // red
          text: "Needs Help",
          description:
            "Looks like Smokey got a bit confused. A quick training refresh should get him back on track.",
          subtext:
            "His last training session failed. Please try retraining. If the problem persists, contact support.",
          progressColor: "bg-red-500",
        };
      default:
        return {
          color: "#6b7280", // gray
          text: "Waiting",
          description:
            "Smokey is waiting for his first lesson. Let's get him trained up!",
          subtext: "No training has been initiated yet.",
          progressColor: "bg-gray-400",
        };
    }
  };

  const handleRowClick = (product: Product) => {
    navigate(`/locations/${location.id}/products/${product.id}`);
  };

  const goToAddProduct = () => {
    navigate(`/locations/${location.id}/products/add`);
  };

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setDropdownOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const smokeyStatus = getSmokeyStatus(vectorStatus);

  // Fast polling when in learning mode (processing)
  const vectorStatusStatus = vectorStatus ? vectorStatus.status : undefined;
  useEffect(() => {
    if (vectorStatus && vectorStatus.status === "processing") {
      const fastInterval = setInterval(() => {
        fetchVectorizationStatus();
      }, 5000); // Poll every 5 seconds when studying

      pollingIntervalRef.current = fastInterval;

      return () => {
        clearInterval(fastInterval);
        pollingIntervalRef.current = null;
      };
    } else if (pollingIntervalRef.current) {
      // Clean up fast polling when not processing
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  }, [fetchVectorizationStatus, vectorStatus, vectorStatusStatus]);

  // Add effect to fetch cost estimate when enhanceLimit changes and modal is open
  useEffect(() => {
    if (isEnhanceOpen && enhanceLimit > 0) {
      fetchCostEstimate(enhanceLimit);
    }
  }, [enhanceLimit, fetchCostEstimate, isEnhanceOpen]);

  // Update handleOpenEnhanceModal to fetch cost estimate
  const handleOpenEnhanceModal = () => {
    setIsEnhanceOpen(true);
    setCostEstimate(null);
    setCostEstimateError(null);
    // Cost estimate will be fetched by the useEffect above
  };

  // Featured products functions - now use data from product results
  const isProductFeatured = (product: EnhancedProduct) => {
    return !!(product as any).is_featured;
  };

  const isProductTopPick = (product: EnhancedProduct) => {
    return !!(product as any).is_top_pick;
  };

  // Bulk action handlers - super simplified, no checking
  const handleBulkAddFeatured = async (selectedItems: EnhancedProduct[]) => {
    try {
      // Just blindly try to add all products as featured
      for (const product of selectedItems) {
        await api.featuredProducts.addFeatured(location.id, {
          product_id: product.id,
          is_top_pick: false,
          active: true,
        });
      }

      toast.success(`Updated ${selectedItems.length} products as featured`);
      state.reload();
    } catch (error) {
      console.error("Error adding featured products:", error);
      toast.error("Failed to add featured products");
    }
  };

  const handleBulkRemoveFeatured = async (selectedItems: EnhancedProduct[]) => {
    try {
      // Just blindly try to remove all products from featured using product IDs
      const productIds = selectedItems.map((product) => product.id);
      console.log(
        "Attempting to remove featured products with IDs:",
        productIds
      );

      try {
        const result = await api.featuredProducts.bulkRemoveFeatured(
          location.id,
          productIds
        );
        console.log("Bulk remove result:", result);
      } catch (bulkError) {
        console.log(
          "Bulk remove failed, trying individual removal:",
          bulkError
        );

        // Fallback: remove each product individually using featured_id if available
        for (const product of selectedItems) {
          if ((product as any).featured_id) {
            try {
              await api.featuredProducts.removeFeatured(
                location.id,
                (product as any).featured_id
              );
            } catch (individualError) {
              console.log(
                `Failed to remove featured product ${product.id}:`,
                individualError
              );
            }
          }
        }
      }

      toast.success(
        `Updated ${selectedItems.length} products (removed from featured)`
      );
      state.reload();
    } catch (error) {
      console.error("Error removing featured products:", error);
      console.error(
        "Error details:",
        (error as any).response?.data || (error as any).message
      );
      toast.error("Failed to remove featured products");
    }
  };

  const handleBulkAddTopPick = async (selectedItems: EnhancedProduct[]) => {
    try {
      // Just blindly try to make all products top picks
      for (const product of selectedItems) {
        await api.featuredProducts.addFeatured(location.id, {
          product_id: product.id,
          is_top_pick: true,
          active: true,
        });
      }

      toast.success(`Updated ${selectedItems.length} products as top picks`);
      state.reload();
    } catch (error) {
      console.error("Error adding top pick products:", error);
      toast.error("Failed to add top pick products");
    }
  };

  const handleBulkRemoveTopPick = async (selectedItems: EnhancedProduct[]) => {
    try {
      // Just blindly try to make all products featured but not top picks
      for (const product of selectedItems) {
        await api.featuredProducts.addFeatured(location.id, {
          product_id: product.id,
          is_top_pick: false,
          active: true,
        });
      }

      toast.success(
        `Updated ${selectedItems.length} products (removed top pick status)`
      );
      state.reload();
    } catch (error) {
      console.error("Error removing top pick products:", error);
      toast.error("Failed to remove top pick products");
    }
  };

  const handleBulkDelete = async (selectedItems: EnhancedProduct[]) => {
    if (
      !window.confirm(
        `Are you sure you want to delete ${selectedItems.length} products? This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      const productIds = selectedItems.map((product) => product.id);

      // Use the bulk delete API for better performance
      const result = await api.products.bulkDelete(location.id, productIds);

      toast.success(
        <div>
          <p>
            <strong>Products Deletion Queued!</strong>
          </p>
          <p className="mt-1">
            {result.deleted_count || selectedItems.length} products have been
            queued for deletion.
          </p>
          <p className="mt-1 text-sm text-gray-600">
            Products will be removed from your inventory shortly.
          </p>
        </div>,
        { duration: 5000 }
      );

      // Immediate reload to clear selection and update UI
      state.reload();

      // Schedule additional reloads to catch async deletions
      // Since deletion is async via jobs, we'll poll a few times to ensure the UI reflects changes
      const reloadTimes = [2000, 5000, 10000]; // 2s, 5s, 10s
      reloadTimes.forEach((delay) => {
        setTimeout(() => {
          state.reload();
        }, delay);
      });
    } catch (error) {
      console.error("Error deleting products:", error);

      // Check if it's a validation error and show specific message
      let errorMessage = "Failed to delete products";

      if (error instanceof Error) {
        if ((error as any).response?.data?.error) {
          // Extract error message from API response
          const apiError = (error as any).response.data.error;
          errorMessage =
            typeof apiError === "string"
              ? apiError
              : apiError.message || errorMessage;
        } else {
          // Use the error message directly
          errorMessage = error.message;
        }
      }

      toast.error(errorMessage);
    }
  };

  // Define bulk actions with improved color scheme
  const bulkActions: BulkAction<EnhancedProduct>[] = [
    {
      key: "add-featured",
      label: "Add to Featured",
      onClick: handleBulkAddFeatured,
      icon: <SparklesIcon />,
      variant: "primary", // Blue for positive actions
    },
    {
      key: "remove-featured",
      label: "Remove from Featured",
      onClick: handleBulkRemoveFeatured,
      icon: <SparklesIcon />,
      variant: "secondary", // Gray for neutral/removal actions
    },
    {
      key: "add-top-picks",
      label: "Add to Top Picks",
      onClick: handleBulkAddTopPick,
      icon: <SparklesIcon />,
      variant: "primary", // Blue for positive actions
    },
    {
      key: "remove-top-picks",
      label: "Remove from Top Picks",
      onClick: handleBulkRemoveTopPick,
      icon: <SparklesIcon />,
      variant: "secondary", // Gray for neutral/removal actions
    },
    {
      key: "delete-products",
      label: "Delete Products",
      onClick: handleBulkDelete,
      icon: <TrashIcon />,
      variant: "destructive", // Red for destructive actions
    },
  ];

  return (
    <>
      <Toaster position="top-center" />
      <PageContent
        title={t("products")}
        actions={
          <div className="flex flex-wrap items-center gap-2">
            <div
              className="flex items-center gap-3 cursor-pointer p-2 rounded-lg transition-colors"
              style={{ cursor: "pointer" }}
              onClick={() => setIsIndexModalOpen(true)}
              title="Click to see Smokey's knowledge status"
            >
              <span className="font-medium text-sm flex items-center gap-1">
                <span>Budtender Status:</span>
              </span>
              <div className="flex items-center gap-2">
                {smokeyStatus.text === "Studying" ? (
                  <Spinner size="small" />
                ) : (
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: smokeyStatus.color }}
                  />
                )}
                <span
                  className="text-sm font-semibold"
                  style={{ color: smokeyStatus.color }}
                >
                  {smokeyStatus.text}
                </span>
              </div>
            </div>
            {/* Primary action button */}
            <Button icon={<PlusIcon />} onClick={goToAddProduct}>
              {t("add_product")}
            </Button>

            {/* Actions dropdown for all secondary actions */}
            <div className="relative" ref={dropdownRef}>
              <Button
                variant="secondary"
                className="flex items-center gap-2"
                onClick={toggleDropdown}
              >
                <div className="flex items-center gap-2">
                  <span>{t("actions")}</span>
                  <span
                    className={`icon-wrapper ${
                      dropdownOpen ? "rotate-icon" : ""
                    }`}
                  >
                    <ChevronDownIcon />
                  </span>
                </div>
              </Button>

              {dropdownOpen && (
                <div className="dropdown-menu">
                  <button
                    className="dropdown-item"
                    onClick={() => {
                      handleOpenEnhanceModal();
                      setDropdownOpen(false);
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <SparklesIcon />
                      <span>Enhance Products</span>
                    </div>
                  </button>

                  <button
                    className="dropdown-item"
                    onClick={() => {
                      setIsPasswordModalOpen(true);
                      setDropdownOpen(false);
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <RefreshIcon />
                      <span>{t("sync_from_marketplace")}</span>
                    </div>
                  </button>

                  <button
                    className="dropdown-item"
                    onClick={() => {
                      setIsUploadOpen(true);
                      setDropdownOpen(false);
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <UploadIcon />
                      <span>{t("upload_products")}</span>
                    </div>
                  </button>

                  <button
                    className="dropdown-item"
                    onClick={() => {
                      setIsMenuSettingsOpen(true);
                      setDropdownOpen(false);
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <SettingsIcon />
                      <span>{t("menu_settings")}</span>
                    </div>
                  </button>
                </div>
              )}
            </div>
          </div>
        }
      >
        <SearchTable
          {...state}
          results={state.results as SearchResult<EnhancedProduct>}
          columns={[
            {
              key: "featured_status",
              title: "Featured",
              sortable: false,
              cell: ({ item }) => (
                <div className="flex flex-row gap-1 items-center justify-center min-w-0">
                  {isProductFeatured(item) && (
                    <Tooltip content="Featured Product">
                      <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center">
                        <SparklesIcon className="w-3 h-3 text-blue-600" />
                      </div>
                    </Tooltip>
                  )}
                  {isProductTopPick(item) && (
                    <Tooltip content="Top Pick">
                      <div className="w-5 h-5 rounded-full bg-yellow-100 flex items-center justify-center">
                        <span className="text-yellow-600 text-xs font-bold">
                          ⭐
                        </span>
                      </div>
                    </Tooltip>
                  )}
                </div>
              ),
            },
            {
              key: "product_name",
              title: t("product_name"),
              sortable: true,
              cell: ({ item }) => (
                <div className="flex items-center min-w-0 flex-1">
                  <span className="truncate" title={item.product_name}>
                    {item.product_name && item.product_name.length > 30
                      ? `${item.product_name.substring(0, 30)}...`
                      : item.product_name}
                  </span>
                </div>
              ),
            },
            {
              key: "brand_name",
              title: t("brand_name"),
              sortable: true,
              cell: ({ item }) => (
                <span className="truncate block">{item.brand_name || "-"}</span>
              ),
            },
            {
              key: "category",
              title: t("category"),
              sortable: true,
              cell: ({ item }) => (
                <span className="truncate block">{item.category || "-"}</span>
              ),
            },
            {
              key: "thc_cbd",
              title: "THC/CBD",
              sortable: false,
              cell: ({ item }) => {
                const thcValue = item.percentage_thc || item.thc || item.mg_thc;
                const cbdValue = item.percentage_cbd || item.cbd || item.mg_cbd;

                return (
                  <div className="flex flex-col text-sm whitespace-nowrap">
                    {thcValue && <span>THC: {thcValue}%</span>}
                    {cbdValue && <span>CBD: {cbdValue}%</span>}
                    {!thcValue && !cbdValue && "-"}
                  </div>
                );
              },
            },
            {
              key: "latest_price",
              title: t("price"),
              sortable: true,
              cell: ({ item }) => {
                const product = item as EnhancedProduct & {
                  out_of_stock?: boolean;
                  inventory_quantity?: number;
                };

                return (
                  <div className="flex flex-col text-sm whitespace-nowrap">
                    <span>
                      {item.latest_price !== null &&
                      item.latest_price !== undefined
                        ? `$${Number(item.latest_price).toFixed(2)}`
                        : "-"}
                    </span>
                    {product.out_of_stock && (
                      <span className="text-xs text-red-600 font-medium">
                        Out of Stock
                      </span>
                    )}
                    {product.inventory_quantity !== undefined &&
                      product.inventory_quantity !== null && (
                        <span className="text-xs text-gray-500">
                          Stock: {product.inventory_quantity}
                        </span>
                      )}
                  </div>
                );
              },
            },
            {
              key: "rating",
              title: t("rating"),
              sortable: true,
              cell: ({ item }) => {
                return item.rating ? (
                  <div className="flex items-center text-sm whitespace-nowrap">
                    <span>★</span>
                    <span className="ml-1">{item.rating}/5</span>
                    {item.reviews_count && (
                      <span className="ml-1 text-gray-500 text-xs">
                        ({item.reviews_count})
                      </span>
                    )}
                  </div>
                ) : (
                  "-"
                );
              },
            },
            {
              key: "is_active",
              title: "Active",
              sortable: true,
              cell: ({ item }) => {
                const isActive = item.is_active !== false; // Default to true for existing products
                return (
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ${
                      isActive
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {isActive ? "Active" : "Inactive"}
                  </span>
                );
              },
            },
            {
              key: "ai_status",
              title: "AI Status",
              sortable: false,
              cell: ({ item }) => (
                <div className="flex items-center justify-center">
                  <AIStatusBadge
                    product={item}
                    size="small"
                    showActions={false}
                  />
                </div>
              ),
            },
          ]}
          enableSearch
          tagEntity="products"
          onRowClick={handleRowClick}
          enableSelection={true}
          bulkActions={bulkActions}
          selectionKey="id"
        />
      </PageContent>

      {/* Upload Products Modal */}
      <Modal
        title={t("upload_products")}
        open={isUploadOpen}
        onClose={() => setIsUploadOpen(false)}
      >
        <FormWrapper<{ file: FileList }>
          onSubmit={async (form) => await uploadProducts(form.file)}
          submitLabel={t("upload")}
        >
          {(form) => (
            <>
              <UploadInstructions
                fields={productFields}
                onDownloadExample={downloadExample}
                acceptedFormat=".csv"
              />
              <UploadField
                form={form}
                name="file"
                label={t("file")}
                accept=".csv"
                required
              />
            </>
          )}
        </FormWrapper>
      </Modal>

      {/* Sync from Marketplace Modal */}
      <Modal
        title="Sync Products from Marketplace"
        open={isSyncOpen}
        onClose={() => !isSyncing && setIsSyncOpen(false)}
      >
        <div className="modal-content">
          <div className="info-message">
            {t("sync_products_description")}
            <strong>{location?.retailer_id || t("not_set")}</strong>
          </div>

          {!location?.retailer_id && (
            <div className="warning-message">
              <p>{t("no_retailer_id_description")}</p>
            </div>
          )}

          <div className="info-message mt-4">
            <p>
              <strong>Note:</strong> By default, this process first scrapes the
              marketplace for fresh data (takes 5+ minutes) before importing.
            </p>
            <p className="mt-1">
              You can skip the live scrape to sync immediately using the data
              from the last successful scrape.
            </p>
          </div>

          <div className="mt-4 mb-4">
            <CheckboxInput
              label="Skip live scrape (sync from database only)"
              checked={skipScrape}
              onChange={(value: boolean) => setSkipScrape(value)}
              disabled={isSyncing}
            />
          </div>

          {isSyncing && (
            <div className="sync-progress">
              <Spinner size="medium" />
              <span>{t("Starting sync process...")}</span>
            </div>
          )}

          <div className="modal-actions">
            <Button
              variant="secondary"
              onClick={() => setIsSyncOpen(false)}
              disabled={isSyncing}
            >
              {t("cancel")}
            </Button>
            <Button
              onClick={syncFromMarketplace}
              disabled={isSyncing || !location?.retailer_id}
            >
              {isSyncing ? t("Please wait...") : t("Sync Products")}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Password Verification Modal */}
      <PasswordVerificationModal
        isOpen={isPasswordModalOpen}
        onClose={() => setIsPasswordModalOpen(false)}
        onVerify={async (password) => {
          const isValid = await verifyMarketplacePassword(password);
          if (isValid) {
            handlePasswordVerified();
          }
          return isValid;
        }}
        title="Marketplace Sync Authorization"
        description="This action requires authorization. Please enter the marketplace sync password to continue."
        isVerifying={isVerifyingPassword}
      />

      {/* Enhance Products Modal */}
      <Modal
        title="Enhance Products with AI"
        open={isEnhanceOpen}
        onClose={() => !isEnhancing && setIsEnhanceOpen(false)}
      >
        <div className="modal-content">
          <div className="info-message">
            <p>
              AI will analyze and enhance your product data by adding detailed
              descriptions, categorizations, and missing information about your
              products.
            </p>
            <p className="mt-2">
              <strong>Enhanced fields may include:</strong>
            </p>
            <ul className="list-disc pl-5 mt-1">
              <li>Product descriptions</li>
              <li>Categories and subcategories</li>
              <li>Product tags</li>
              <li>Effects and moods</li>
              <li>THC/CBD percentages (estimates)</li>
            </ul>

            <div className="bg-blue-100 p-3 rounded mt-4">
              <p className="text-blue-800">
                <strong>Note:</strong> Processing runs in the background and may
                take several minutes or hours depending on the number of
                products and the complexity of the data.
              </p>
            </div>
          </div>
          {/* High Cost Warning */}
          {costEstimate && costEstimate.estimatedCost > 0.2 && (
            <div className="mt-1 mb-1">
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                <div className="flex items-center">
                  <span className="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium bg-amber-500 text-white mr-3 text-center">
                    High Cost
                  </span>
                  <span className="text-amber-800 text-sm">
                    Estimated cost: ${costEstimate.estimatedCost.toFixed(2)} for{" "}
                    {costEstimate.deduplicatedCount} products
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="mt-1 mb-1">
            <TextInput
              name="enhanceLimit"
              label="Number of products to enhance"
              value={enhanceLimit.toString()}
              onChange={(value) => {
                const numValue = parseInt(value);
                if (!isNaN(numValue) && numValue > 0) {
                  setEnhanceLimit(numValue);
                }
              }}
              type="number"
              min="1"
              max="4000"
              disabled={isEnhancing}
            />
            <p className="text-sm text-gray-500 mt-1">
              Products without enhancement will be processed first
            </p>
          </div>

          {isEnhancing && (
            <div className="enhance-progress mt-4">
              <Spinner size="medium" />
              <span>Queuing product enhancement job...</span>
            </div>
          )}

          <div className="modal-actions mt-6">
            <Button
              variant="secondary"
              onClick={() => setIsEnhanceOpen(false)}
              disabled={isEnhancing}
            >
              {t("cancel")}
            </Button>
            <Button
              onClick={enhanceProducts}
              disabled={isEnhancing || isLoadingCostEstimate}
              icon={<SparklesIcon />}
            >
              {isEnhancing ? "Starting..." : "Enhance Products"}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Index Management Modal */}
      <Modal
        title="Smokey's Knowledge Status"
        open={isIndexModalOpen}
        onClose={() => setIsIndexModalOpen(false)}
      >
        <div className="modal-content p-1">
          <div className="mb-6">
            {vectorStatus ? (
              <div className="p-4 bg-gray-50 rounded-lg border">
                <div className="flex items-center mb-3">
                  <div
                    className="w-4 h-4 rounded-full mr-3"
                    style={{ backgroundColor: smokeyStatus.color }}
                  ></div>
                  <p className="font-semibold text-gray-800">
                    {smokeyStatus.description}
                  </p>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                  <div
                    className={`${smokeyStatus.progressColor} h-2.5 rounded-full transition-all duration-500`}
                    style={{
                      width: `${Math.min(
                        vectorStatus.completion_percentage ?? 0,
                        100
                      )}%`,
                    }}
                  ></div>
                </div>
                <div className="text-right text-xs font-medium text-gray-600 mb-3">
                  {vectorStatus.completion_percentage ?? 0}% Complete
                </div>

                <div className="mt-3 pt-3 border-t text-sm text-gray-600">
                  {smokeyStatus.subtext}
                </div>
              </div>
            ) : (
              <div className="text-gray-500">Loading status...</div>
            )}
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Actions</h3>
            <div className="space-y-4">
              <div className="p-4 border border-orange-300 rounded-lg bg-orange-50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-semibold text-orange-800">
                      Retrain Smokey
                    </p>
                    <p className="text-sm text-orange-700 mt-1">
                      This will rebuild Smokey's knowledge from scratch. Use
                      this if his knowledge seems out of date.
                    </p>
                  </div>
                  <Button
                    variant="secondary"
                    onClick={() => {
                      if (
                        window.confirm(
                          "Are you sure you want to retrain Smokey? This will rebuild his knowledge base and can take a few minutes."
                        )
                      ) {
                        startReVectorization();
                        setIsIndexModalOpen(false);
                      }
                    }}
                    disabled={isVectorizing || isBulkVectorizing}
                    className="ml-4 flex-shrink-0"
                  >
                    {isVectorizing ? "Retraining..." : "Retrain"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Modal>

      {/* Menu Settings Modal */}
      <MenuSettingsModal
        isOpen={isMenuSettingsOpen}
        onClose={() => setIsMenuSettingsOpen(false)}
        locationId={location.id}
      />
    </>
  );
}
