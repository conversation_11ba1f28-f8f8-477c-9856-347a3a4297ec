import { Combobox } from "@headlessui/react";
import { useState, useMemo } from "react";
import { CheckIcon, ChevronUpDownIcon } from "../icons";
import { usePopperSelectDropdown } from "../utils";
import { ControlledInputProps } from "../../types";
import clsx from "clsx";
import "./Select.css";

export interface CategorySelectProps extends ControlledInputProps<string> {
  className?: string;
  buttonClassName?: string;
  onBlur?: () => void;
  size?: "small" | "regular";
  variant?: "plain" | "minimal";
}

// Predefined categories as requested
const DEFAULT_CATEGORIES = [
  "Flower",
  "Pre-Rolls", 
  "Vapes",
  "Edibles",
  "Concentrates",
  "CBD",
  "Topicals",
  "Tinctures",
  "Apparel",
  "Accessories"
];

export function CategorySelect({
  buttonClassName,
  className,
  disabled,
  error,
  hideLabel,
  label,
  onBlur,
  onChange,
  required,
  size,
  subtitle,
  value,
  variant,
}: CategorySelectProps) {
  const [query, setQuery] = useState("");
  const { setReferenceElement, setPopperElement, attributes, styles } =
    usePopperSelectDropdown();

  // Filter categories based on query and include custom option if query doesn't match exactly
  const filteredOptions = useMemo(() => {
    const filtered = DEFAULT_CATEGORIES.filter((category) =>
      category.toLowerCase().includes(query.toLowerCase())
    );
    
    // If query exists and doesn't exactly match any category, add it as a custom option
    if (query && !DEFAULT_CATEGORIES.some(cat => cat.toLowerCase() === query.toLowerCase())) {
      filtered.push(query);
    }
    
    return filtered;
  }, [query]);

  const selectedOption = DEFAULT_CATEGORIES.find(cat => cat === value) || value;

  return (
    <Combobox
      as="div"
      className={clsx("ui-select", className, variant ?? "plain")}
      disabled={disabled}
      value={value || ""}
      onChange={onChange}
    >
      {label && (
        <Combobox.Label style={hideLabel ? { display: "none" } : undefined}>
          <span>
            {label}
            {required && <span style={{ color: "red" }}>&nbsp;*</span>}
          </span>
        </Combobox.Label>
      )}
      {subtitle && <span className="label-subtitle">{subtitle}</span>}
      
      <div className="ui-button-group">
        <span
          className={clsx("ui-text-input", size ?? "regular", { 
            disabled,
            error: !!error 
          })}
          style={{ flexGrow: 1 }}
        >
          <Combobox.Input
            displayValue={(value: string) => value || ""}
            onChange={(e) => setQuery(e.target.value)}
            onBlur={onBlur}
            placeholder="Select or type a category..."
            ref={setReferenceElement}
          />
        </span>
        <Combobox.Button
          className={clsx("ui-button", "secondary", size ?? "regular", buttonClassName)}
        >
          <ChevronUpDownIcon />
        </Combobox.Button>
      </div>

      <Combobox.Options
        className="select-options nowheel"
        ref={setPopperElement}
        style={styles.popper}
        {...attributes.popper}
      >
        {filteredOptions.map((option) => {
          const isCustom = !DEFAULT_CATEGORIES.includes(option);
          return (
            <Combobox.Option
              key={option}
              value={option}
              className={({ active, selected }) =>
                clsx(
                  "select-option",
                  active && "active",
                  selected && "selected"
                )
              }
            >
              <span>
                {option}
                {isCustom && <span className="text-gray-500 ml-2">(custom)</span>}
              </span>
              <span className="option-icon">
                <CheckIcon aria-hidden="true" />
              </span>
            </Combobox.Option>
          );
        })}
        {filteredOptions.length === 0 && query && (
          <div className="select-option disabled">
            No categories found
          </div>
        )}
      </Combobox.Options>
      
      {error && <div className="input-error">{error}</div>}
    </Combobox>
  );
}
