import React from "react";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";

/**
 * Utility function to convert plain text with formatting cues to markdown
 * This handles common patterns found in product descriptions copied from external sources
 */
const convertPlainTextToMarkdown = (text: string): string => {
  if (!text) return text;

  let converted = text;

  // Normalize line endings
  converted = converted.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  // Convert section headers (text followed by colon and newline) to bold
  converted = converted.replace(/^([^:\n]+:)\s*$/gm, '**$1**');

  // Convert lines that start with checkmarks or fire emojis to proper list items
  converted = converted.replace(/^(✔️|🔥)\s*(.+)$/gm, '- $1 $2');

  // Preserve existing double line breaks
  converted = converted.replace(/\n\n+/g, '\n\n');

  return converted;
};

interface MarkdownRendererProps {
  content: string;
  className?: string;
  compact?: boolean; // For smaller text rendering
  autoFormat?: boolean; // Whether to auto-convert plain text to markdown
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = "",
  compact = false,
  autoFormat = false,
}) => {
  const baseFontSize = compact ? "0.9rem" : "1rem";
  const headingScale = compact ? 0.8 : 1;

  // Auto-convert plain text to markdown if requested
  const processedContent = autoFormat ? convertPlainTextToMarkdown(content) : content;

  // If content doesn't contain markdown syntax and autoFormat is enabled,
  // render as formatted plain text
  const hasMarkdownSyntax = /(\*\*|__|\*|_|#{1,6}\s|```|`|\[.*\]\(.*\)|^>\s)/m.test(processedContent);

  if (autoFormat && !hasMarkdownSyntax) {
    return (
      <div
        className={`markdown-renderer plain-text ${className}`}
        style={{
          fontSize: baseFontSize,
          lineHeight: "1.6",
          whiteSpace: "pre-wrap",
        }}
      >
        {processedContent.split('\n').map((line, index) => {
          const trimmedLine = line.trim();

          // Handle section headers (bold text ending with colon)
          if (trimmedLine.match(/^.+:$/)) {
            return (
              <div key={index} style={{
                fontWeight: "bold",
                marginTop: index > 0 ? (compact ? "0.75rem" : "1rem") : "0",
                marginBottom: compact ? "0.25rem" : "0.5rem",
                fontSize: compact ? "0.95rem" : "1.05rem"
              }}>
                {trimmedLine}
              </div>
            );
          }

          // Handle list items with emojis
          if (trimmedLine.match(/^(✔️|🔥)\s/)) {
            return (
              <div key={index} style={{
                marginLeft: compact ? "0.75rem" : "1rem",
                marginBottom: "0.25rem",
                display: "flex",
                alignItems: "flex-start"
              }}>
                <span style={{ marginRight: "0.5rem", flexShrink: 0 }}>
                  {trimmedLine.match(/^(✔️|🔥)/)?.[0]}
                </span>
                <span>{trimmedLine.replace(/^(✔️|🔥)\s*/, '')}</span>
              </div>
            );
          }

          // Handle empty lines (create spacing)
          if (trimmedLine === '') {
            return <div key={index} style={{ height: compact ? "0.5rem" : "0.75rem" }} />;
          }

          // Regular text paragraphs
          return (
            <div key={index} style={{
              marginBottom: compact ? "0.5rem" : "0.75rem",
              lineHeight: "1.6"
            }}>
              {trimmedLine}
            </div>
          );
        })}
      </div>
    );
  }

  return (
    <ReactMarkdown
      rehypePlugins={[rehypeRaw]}
      className={`markdown-renderer ${className}`}
      components={{
        h1: ({ node, ...props }) => (
          <h1
            style={{
              fontSize: `${1.4 * headingScale}rem`,
              marginTop: compact ? "0.5rem" : "1rem",
              marginBottom: "0.5rem",
              fontWeight: "bold",
              lineHeight: "1.3",
            }}
            {...props}
          />
        ),
        h2: ({ node, ...props }) => (
          <h2
            style={{
              fontSize: `${1.3 * headingScale}rem`,
              marginTop: compact ? "0.5rem" : "1rem",
              marginBottom: "0.5rem",
              fontWeight: "bold",
              lineHeight: "1.3",
            }}
            {...props}
          />
        ),
        h3: ({ node, ...props }) => (
          <h3
            style={{
              fontSize: `${1.1 * headingScale}rem`,
              marginTop: compact ? "0.4rem" : "0.75rem",
              marginBottom: "0.5rem",
              fontWeight: "bold",
              lineHeight: "1.3",
            }}
            {...props}
          />
        ),
        h4: ({ node, ...props }) => (
          <h4
            style={{
              fontSize: `${1.05 * headingScale}rem`,
              marginTop: compact ? "0.4rem" : "0.75rem",
              marginBottom: "0.5rem",
              fontWeight: "bold",
              lineHeight: "1.3",
            }}
            {...props}
          />
        ),
        h5: ({ node, ...props }) => (
          <h5
            style={{
              fontSize: baseFontSize,
              marginTop: compact ? "0.3rem" : "0.5rem",
              marginBottom: "0.5rem",
              fontWeight: "bold",
              lineHeight: "1.3",
            }}
            {...props}
          />
        ),
        h6: ({ node, ...props }) => (
          <h6
            style={{
              fontSize: baseFontSize,
              marginTop: compact ? "0.3rem" : "0.5rem",
              marginBottom: "0.5rem",
              fontWeight: "bold",
              lineHeight: "1.3",
            }}
            {...props}
          />
        ),
        p: ({ node, ...props }) => (
          <p
            style={{
              fontSize: baseFontSize,
              lineHeight: "1.6",
              marginBottom: compact ? "0.75rem" : "1rem",
              whiteSpace: "pre-wrap", // Preserve line breaks and spaces
            }}
            {...props}
          />
        ),
        blockquote: ({ node, ...props }) => (
          <blockquote
            style={{
              borderLeft: "4px solid #ddd",
              paddingLeft: "1rem",
              margin: compact ? "0.5rem 0" : "0.75rem 0",
              color: "#666",
              fontStyle: "italic",
            }}
            {...props}
          />
        ),
        hr: ({ node, ...props }) => (
          <hr
            style={{
              margin: compact ? "0.75rem 0" : "1rem 0",
              border: "none",
              height: "1px",
              backgroundColor: "#ddd",
            }}
            {...props}
          />
        ),
        ul: ({ node, ...props }) => (
          <ul
            style={{
              paddingLeft: "1.5rem",
              marginBottom: compact ? "0.5rem" : "0.75rem",
              fontSize: baseFontSize,
            }}
            {...props}
          />
        ),
        ol: ({ node, ...props }) => (
          <ol
            style={{
              paddingLeft: "1.5rem",
              marginBottom: compact ? "0.5rem" : "0.75rem",
              fontSize: baseFontSize,
            }}
            {...props}
          />
        ),
        li: ({ node, ...props }) => (
          <li
            style={{
              marginBottom: "0.25rem",
              lineHeight: "1.5",
            }}
            {...props}
          />
        ),
        strong: ({ node, ...props }) => (
          <strong
            style={{
              fontWeight: "600",
            }}
            {...props}
          />
        ),
        em: ({ node, ...props }) => (
          <em
            style={{
              fontStyle: "italic",
            }}
            {...props}
          />
        ),
        code: ({ node, className, children, ...props }: any) => {
          const match = /language-(\w+)/.exec(className || "");
          const isInline = !match && !className;
          const fontSize = compact ? "0.8rem" : "0.9rem";

          return isInline ? (
            <code
              style={{
                backgroundColor: "#f0f0f0",
                padding: "0.1rem 0.3rem",
                borderRadius: "3px",
                fontSize,
                fontFamily: "monospace",
              }}
              {...props}
            >
              {children}
            </code>
          ) : (
            <code
              style={{
                display: "block",
                backgroundColor: "#f5f5f5",
                padding: compact ? "0.4rem 0.8rem" : "0.5rem 1rem",
                borderRadius: "4px",
                overflowX: "auto",
                fontSize,
                fontFamily: "monospace",
                marginBottom: compact ? "0.5rem" : "0.75rem",
              }}
              {...props}
            >
              {children}
            </code>
          );
        },
        table: ({ node, ...props }) => (
          <table
            style={{
              borderCollapse: "collapse",
              margin: compact ? "0.5rem 0" : "1rem 0",
              width: "100%",
              fontSize: baseFontSize,
            }}
            {...props}
          />
        ),
        th: ({ node, ...props }) => (
          <th
            style={{
              border: "1px solid #ddd",
              padding: compact ? "0.4rem" : "0.5rem",
              backgroundColor: "#f8f9fa",
              fontWeight: "bold",
              textAlign: "left",
            }}
            {...props}
          />
        ),
        td: ({ node, ...props }) => (
          <td
            style={{
              border: "1px solid #ddd",
              padding: compact ? "0.4rem" : "0.5rem",
            }}
            {...props}
          />
        ),
        a: ({ node, ...props }) => (
          <a
            style={{
              color: "#007bff",
              textDecoration: "underline",
            }}
            target="_blank"
            rel="noopener noreferrer"
            {...props}
          />
        ),
      }}
    >
      {processedContent}
    </ReactMarkdown>
  );
};

export default MarkdownRenderer;
