import React, { useState, useCallback } from "react";
import TextInput from "./form/TextInput";
import MarkdownRenderer from "./MarkdownRenderer";
import Button from "./Button";
import { FiEye, FiEdit3, FiInfo } from "react-icons/fi";
import "./MarkdownEditor.css";

interface MarkdownEditorProps {
  label: string;
  name: string;
  value: string;
  onChange: (value: string) => void;
  required?: boolean;
  subtitle?: string;
  placeholder?: string;
  rows?: number;
}

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  label,
  name,
  value,
  onChange,
  required = false,
  subtitle,
  placeholder,
  rows = 4,
}) => {
  const [showPreview, setShowPreview] = useState(false);

  const togglePreview = useCallback(() => {
    setShowPreview(!showPreview);
  }, [showPreview]);

  const handleChange = useCallback(
    (newValue: string) => {
      onChange(newValue);
    },
    [onChange]
  );

  const hasContent = value && value.trim().length > 0;

  return (
    <div className="markdown-editor">
      <div className="markdown-editor-header">
        <label className="form-label">
          {label}
          {required && <span className="required">*</span>}
        </label>
        <div className="markdown-editor-actions">
          {hasContent && (
            <Button
              type="button"
              variant="secondary"
              size="small"
              icon={showPreview ? <FiEdit3 /> : <FiEye />}
              onClick={togglePreview}
            >
              {showPreview ? "Edit" : "Preview"}
            </Button>
          )}
        </div>
      </div>

      {subtitle && <div className="form-subtitle mb-2">{subtitle}</div>}

      <div className="markdown-editor-help">
        <div className="flex items-center text-sm text-gray-600 mb-2">
          <FiInfo className="mr-1" size={14} />
          <span>Supports Markdown and HTML formatting</span>
        </div>
      </div>

      {showPreview && hasContent ? (
        <div className="markdown-preview-container">
          <div className="markdown-preview">
            <MarkdownRenderer content={value} compact autoFormat={true} />
          </div>
        </div>
      ) : (
        <TextInput
          name={name}
          value={value}
          onChange={handleChange}
          textarea
          placeholder={placeholder || "Enter markdown or HTML content..."}
        />
      )}

      <div className="markdown-editor-examples">
        <details className="markdown-help">
          <summary className="text-sm text-gray-600 cursor-pointer mb-2">
            Show formatting examples
          </summary>
          <div className="markdown-examples text-xs text-gray-500">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <strong>Markdown:</strong>
                <pre className="bg-gray-100 p-2 rounded mt-1">
                  {`**Bold text**
*Italic text*
# Heading 1
## Heading 2
- List item
- Another item`}
                </pre>
              </div>
              <div>
                <strong>HTML:</strong>
                <pre className="bg-gray-100 p-2 rounded mt-1">
                  {`<strong>Bold text</strong>
<em>Italic text</em>
<h3>Heading</h3>
<ul>
  <li>List item</li>
</ul>`}
                </pre>
              </div>
            </div>
          </div>
        </details>
      </div>
    </div>
  );
};

export default MarkdownEditor;
