# Open Source Chat Framework Replacement PRP

## Executive Summary

Replace the current bespoke React/Koa chat implementation with an open-source, full-stack chat framework that supports file upload, runtime model-switching, and integrates with existing LangChain/TypeScript tools through LangServe. The solution must preserve the TypeScript monorepo structure and existing Docker workflows while scaling from ~30 to 100 customers within a year.

**Confidence Score: 8/10** - Leverages maintained OSS frameworks and fits current stack. Risks: GPU capacity and Pillow upload quirks.

## Research Process

### 1. Codebase Analysis

| Component            | Current State                                           | Migration Strategy                                   |
| -------------------- | ------------------------------------------------------- | ---------------------------------------------------- |
| `docker-compose.yml` | Defines api/worker/ui services with MySQL/Redis         | **KEEP** - Add chat-runtime and inference services   |
| `SmokeyAIService.ts` | Monolithic class handling routing, prompts, tool calls  | **REPLACE** - LangServe endpoint + LangGraph planner |
| `ChatWebSocket.ts`   | Custom WebSocket implementation for real-time messaging | **REPLACE** - Framework's native WebSocket support   |
| `ChatController.ts`  | File upload backend exists but UI widget commented out  | **REPLACE** - Framework's native file upload         |
| `tools/` directory   | LangChain tools with proper interfaces                  | **KEEP** - Expose via LangServe                      |
| `CrewAIService.ts`   | Multi-agent orchestration service                       | **KEEP** - Integrate via LangServe                   |
| `ModernChat_new.tsx` | React chat UI without SSE streaming                     | **REPLACE** - Embed framework via iframe             |

### 2. External Research Findings

| Technology     | Documentation                                                                              | Key Benefits                                      |
| -------------- | ------------------------------------------------------------------------------------------ | ------------------------------------------------- |
| **Open WebUI** | [Open WebUI Docs](https://docs.openwebui.com/features/workspace/models/)                   | Native file upload, model switching, Docker-ready |
| **LangServe**  | [LangChain Docs](https://python.langchain.com/docs/langserve/)                             | REST API for TypeScript tools, OpenAPI spec       |
| **LangGraph**  | [LangChain Tutorial](https://langchain-ai.github.io/langgraph/tutorials/plan-and-execute/) | Multi-tool orchestration, plan-and-execute        |
| **OpenAI API** | [OpenAI API Docs](https://platform.openai.com/docs/api-reference)                          | GPT-4o mini: $0.15 in / $0.60 out per M tokens    |
| **Langfuse**   | [Langfuse Docs](https://langfuse.com/docs/tracing)                                         | OSS observability, tracing + cost dashboards      |

### 3. Critical Context & Gotchas

- **Tool naming**: Must match `^[a-zA-Z0-9_-]{1,64}$` or OpenAI returns 400 errors
- **WebUI uploads**: Requires `libjpeg-turbo-dev zlib-dev` in Docker image for Pillow support
- **OpenAI API**: Direct connection to `https://api.openai.com/v1` (no proxy needed)
- **LangServe integration**: Existing TypeScript tools need REST wrapper
- **Authentication**: Preserve current Firebase/JWT auth flow with origin checks
- **Iframe security**: Add `Cross-Origin-Embedder-Policy` and origin validation

## Implementation Blueprint

### Architecture Overview

```mermaid
graph LR
  subgraph "Current Stack"
    A[React UI] --> B[Koa API]
    B --> C[SmokeyAIService]
    C --> D[LangChain Tools]
  end

     subgraph "New Stack"
     E[React UI] --> F[Open WebUI iframe]
     F --> G[LangServe /chat]
     G --> H[LangGraph Planner]
     H --> I[TypeScript Tools]
     F --> J[OpenAI API]
     G -.trace.-> K[Langfuse]
   end
```

### Pseudocode Implementation

```typescript
// apps/langserve/src/server.ts
import { createServer } from "langserve";
import { ChatAgent } from "./agents/ChatAgent";
import { LangGraphPlanner } from "./planners/LangGraphPlanner";

const server = createServer({
  "/chat": new ChatAgent({
    planner: new LangGraphPlanner(),
    tools: await loadTypeScriptTools(),
    model: process.env.INFERENCE_API || "openai",
  }),
});

// apps/langserve/src/agents/ChatAgent.ts
export class ChatAgent {
  async invoke(message: string, context: ChatContext) {
    const plan = await this.planner.createPlan(message, context);
    const results = await this.planner.execute(plan);
    return this.formatResponse(results);
  }
}
```

## Step-by-Step Implementation Tasks

### Phase 1: Infrastructure Setup (Week 1)

1. **Add chat-runtime service to docker-compose.yml**

   ```yaml
   chat-runtime:
     image: ghcr.io/open-webui/openwebui:latest
     ports:
       - "3002:8080"
     environment:
       - OPENAI_API_BASE_URL=https://api.openai.com/v1
       - OPENAI_API_KEY=${OPENAI_API_KEY}
       - WEBUI_SECRET_KEY=${WEBUI_SECRET_KEY}
       - DEFAULT_MODELS=openai/gpt-4o-mini,openai/gpt-3.5-turbo
       - ENABLE_OAUTH_SIGNUP=false
       - ENABLE_SIGNUP=false
     volumes:
       - ./webui/config:/data
       - uploads:/app/backend/data/uploads
     depends_on:
       - langserve

   langserve:
     build:
       context: .
       dockerfile: docker/langserve.Dockerfile
     ports:
       - "7000:7000"
     environment:
       - OPENAI_API_KEY=${OPENAI_API_KEY}
       - DB_HOST=mysql
       - DB_USERNAME=${DB_USERNAME}
       - DB_PASSWORD=${DB_PASSWORD}
       - DB_DATABASE=${DB_DATABASE}
       - SUPABASE_URL=${SUPABASE_URL}
       - SUPABASE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
       - PINECONE_API_KEY=${PINECONE_API_KEY}
     depends_on:
       mysql:
         condition: service_healthy
   ```

2. **Update environment variables**

   ```bash
   # Add to .env
   LANGSERVE_PORT=7000
   WEBUI_PORT=3002
   WEBUI_SECRET_KEY=your_webui_secret_key_here
   WEBUI_ORIGIN=http://localhost:3002
   ```

3. **Create LangServe Dockerfile**

   ```dockerfile
   # docker/langserve.Dockerfile
   FROM node:18-alpine

   WORKDIR /app

   # Install Python for LangServe and image processing dependencies
   RUN apk add --no-cache python3 py3-pip python3-dev build-base libjpeg-turbo-dev zlib-dev

   # Copy package files
   COPY apps/langserve/package*.json ./
   RUN npm install

   # Install Python dependencies
   COPY apps/langserve/requirements.txt ./
   RUN pip3 install -r requirements.txt

   # Copy source code
   COPY apps/langserve/src ./src
   COPY apps/platform/src/tools ./tools
   COPY apps/platform/src/config ./config

   EXPOSE 7000
   CMD ["npm", "start"]
   ```

4. **Scaffold LangServe server structure**
   ```bash
   mkdir -p apps/langserve/src/{agents,planners,tools,auth}
   touch apps/langserve/package.json
   touch apps/langserve/requirements.txt
   touch apps/langserve/src/server.ts
   ```

### Phase 2: LangServe Integration (Week 2)

4. **Create LangServe server with TypeScript tool export**

   ```typescript
   // apps/langserve/src/server.ts
   import { createServer } from "langserve";
   import { ChatAgent } from "./agents/ChatAgent";
   import { exportTypeScriptTools } from "./tools/exporter";

   const tools = await exportTypeScriptTools();
   const agent = new ChatAgent(tools);

   const server = createServer({
     "/chat": agent,
     "/tools": tools,
   });
   ```

5. **Build LangGraph planner**

   ```typescript
   // apps/langserve/src/planners/LangGraphPlanner.ts
   import { StateGraph, END } from "@langchain/langgraph";

   export class LangGraphPlanner {
     async createPlan(message: string, context: any) {
       // Implement plan-and-execute pattern
       const plan = await this.llm.invoke(`
         Create a step-by-step plan for: ${message}
         Available tools: ${this.tools.map((t) => t.name).join(", ")}
       `);
       return this.parsePlan(plan);
     }
   }
   ```

6. **Export existing TypeScript tools**

   ```typescript
   // apps/langserve/src/tools/exporter.ts
   import {
     DataHubTool,
     VectorSearchTool,
     AnalysisTool,
   } from "../../platform/src/tools";

   export async function exportTypeScriptTools() {
     return [
       new DataHubTool(db, supabaseUrl, supabaseKey),
       new VectorSearchTool(pineconeClient, openai, indexName),
       new AnalysisTool(dataHubTool),
     ];
   }
   ```

### Phase 3: UI Integration (Week 3)

7. **Embed Open WebUI in React app**

   ```typescript
   // apps/ui/src/views/chat/ModernChat_new.tsx
   const ChatFrame: React.FC = () => {
     const [authToken, setAuthToken] = useState<string>();
     const iframeRef = useRef<HTMLIFrameElement>(null);

     useEffect(() => {
       // Get auth token from current session
       const token = getAuthToken();
       setAuthToken(token);

       // Setup secure postMessage communication
       const handleMessage = (event: MessageEvent) => {
         // Validate origin for security
         if (event.origin !== process.env.REACT_APP_WEBUI_ORIGIN) {
           console.warn(
             "Rejected message from untrusted origin:",
             event.origin
           );
           return;
         }

         // Handle authenticated messages from Open WebUI
         if (event.data.type === "auth_request") {
           iframeRef.current?.contentWindow?.postMessage(
             {
               type: "auth_response",
               token: authToken,
               locationId: getCurrentLocation().id,
             },
             process.env.REACT_APP_WEBUI_ORIGIN
           );
         }
       };

       window.addEventListener("message", handleMessage);
       return () => window.removeEventListener("message", handleMessage);
     }, [authToken]);

     return (
       <iframe
         ref={iframeRef}
         src={`${process.env.REACT_APP_WEBUI_URL}/chat`}
         className="chat-frame"
         title="AI Chat"
         sandbox="allow-same-origin allow-scripts allow-forms"
       />
     );
   };
   ```

8. **Configure WebUI for file upload and model switching**

   ```yaml
   # webui/config/webui.yaml
   default_models:
     - openai/gpt-4o-mini
     - openai/gpt-3.5-turbo

   upload:
     enabled: true
     max_size: 10MB
     allowed_types: [pdf, png, jpg, jpeg]

   # Model provider configuration
   providers:
     openai:
       api_key: ${OPENAI_API_KEY}
   ```

### Phase 4: Observability & Evaluation (Week 4)

9. **Integrate Langfuse for tracing**

   ```typescript
   // apps/langserve/src/tracing/langfuse.ts
   import { Langfuse } from "langfuse";

   const langfuse = new Langfuse({
     publicKey: process.env.LANGFUSE_PUBLIC_KEY,
     secretKey: process.env.LANGFUSE_SECRET_KEY,
     host: process.env.LANGFUSE_HOST,
   });

   export async function traceChat(message: string, response: string) {
     const trace = langfuse.trace({
       name: "chat_completion",
       metadata: { message, response },
     });
     await trace.update({ status: "success" });
   }
   ```

10. **Setup TogetherAI for fine-tuning**

    ```python
    # apps/finetuning/together_ai_setup.py
    import together
    from langfuse import Langfuse

    def setup_together_finetuning():
        # Configure TogetherAI
        together.api_key = os.getenv("TOGETHER_API_KEY")

        # Get training data from Langfuse
        langfuse = Langfuse()
        chat_logs = langfuse.get_traces()

        # Prepare training dataset
        training_data = prepare_cannabis_dataset(chat_logs)

        # Start fine-tuning job
        job = together.Finetune.create(
            training_data=training_data,
            model="meta-llama/Llama-3-8b",
            hyperparameters={
                "epochs": 3,
                "learning_rate": 2e-4,
                "batch_size": 4
            }
        )

        return job
    ```

### Phase 5: Fine-tuning & Optimization (Week 5)

11. **Setup TogetherAI fine-tuning workflow**

    ```python
    # scripts/finetune_together.py
    import together
    import os

    def finetune_cannabis_model():
        # Configure TogetherAI
        together.api_key = os.getenv("TOGETHER_API_KEY")

        # Prepare training data from chat logs
        training_data = prepare_training_data()

        # Start fine-tuning job
        job = together.Finetune.create(
            training_data=training_data,
            model="meta-llama/Llama-3-8b",
            hyperparameters={
                "epochs": 3,
                "learning_rate": 2e-4,
                "batch_size": 4,
                "lora_r": 16,
                "lora_alpha": 32
            }
        )

        print(f"Fine-tuning job started: {job.id}")
        return job
    ```

12. **Add load testing and monitoring**
    ```bash
    # scripts/load_test.sh
    k6 run load/chat.lua --vus 50 --duration 5m
    ```

## Validation Gates

### Syntax & Types

```bash
# TypeScript compilation
pnpm tsc --noEmit

# ESLint
pnpm eslint "apps/**/*.{ts,tsx}" --fix

# LangServe contract validation
curl -f http://localhost:7000/docs | jq '.paths."/chat"'
```

### Unit Tests

```bash
# Run all tests
pnpm vitest run

# Test specific components
pnpm vitest run apps/langserve/src/__tests__/
```

### Integration Tests

```bash
# Test chat flow
npx zx scripts/test-chat-flow.mjs

# Test file upload
npx zx scripts/test-file-upload.mjs

# Test model switching
npx zx scripts/test-model-switch.mjs
```

### Load Testing

```bash
# 50 virtual users, P95 < 750ms
k6 run load/chat.lua --vus 50 --duration 5m

# Stream response time < 1s for first token
npx zx scripts/test-stream.mjs
```

### E2E Tests

```bash
# Full chat workflow
npm run test:e2e:chat

# File upload workflow
npm run test:e2e:upload

# Model switching workflow
npm run test:e2e:model-switch
```

## Error Handling Strategy

### LangServe Errors

```typescript
// apps/langserve/src/error-handling.ts
export class LangServeErrorHandler {
  static async handleToolError(error: Error, toolName: string) {
    logger.error({ error, toolName }, "Tool execution failed");

    // Fallback to simpler response
    return {
      content:
        "I'm having trouble accessing that information right now. Let me help you with something else.",
      error: true,
    };
  }
}
```

### WebUI Integration Errors

```typescript
// apps/ui/src/views/chat/error-boundary.tsx
export class ChatErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error
    logger.error({ error, errorInfo }, "Chat frame error");

    // Show fallback UI
    this.setState({ hasError: true });
  }

  render() {
    if (this.state.hasError) {
      return <ChatFallback onRetry={this.handleRetry} />;
    }
    return this.props.children;
  }
}
```

## Monitoring & Observability

### Langfuse Integration

```typescript
// apps/langserve/src/monitoring/langfuse.ts
export class ChatMonitoring {
  static async traceChatSession(
    sessionId: string,
    message: string,
    response: string
  ) {
    const trace = langfuse.trace({
      name: "chat_session",
      sessionId,
      metadata: { message, response },
    });

    // Track tool usage
    if (response.toolResults) {
      response.toolResults.forEach((tool) => {
        trace.span({
          name: `tool_${tool.name}`,
          metadata: { result: tool.result },
        });
      });
    }
  }
}
```

### Phoenix Evaluation

```python
# apps/evaluation/phoenix_config.py
import phoenix as px

def setup_phoenix_eval():
    # Configure evaluation metrics
    px.set_eval_metrics([
        "relevance",
        "helpfulness",
        "factual_accuracy",
        "response_time"
    ])

    # Setup automated evaluation
    px.schedule_eval(
        frequency="daily",
        dataset="chat_logs",
        metrics=["relevance", "helpfulness"]
    )
```

## Cost Optimization Plan

### Current GPT-4o Mini Pricing (OpenAI)

| Usage Level          | Tokens/Month | Cost/Month  | Notes                            |
| -------------------- | ------------ | ----------- | -------------------------------- |
| **30 customers**     | 5M tokens    | **$3,750**  | Current scale, manageable cost   |
| **100 customers**    | 15M tokens   | **$11,250** | Target scale, still reasonable   |
| **Break-even point** | 25M tokens   | **$18,750** | When self-hosting becomes viable |

**Pricing**: $0.15 per M input tokens, $0.60 per M output tokens (~$0.75/M average)

### Realistic Cost Targets

- **Current realistic target**: $0.03 per chat interaction (350 in+out tokens)
- **Previous target of <$0.01** was unrealistic with GPT-4o mini
- **Monitor usage**: Implement caching for repeated queries

### Future Optimization Strategy

When hitting ~25M tokens/month, consider:

- **Groq Cloud**: Llama-3 8B Instant ($0.13/Mtok) - 70% cost reduction
- **TogetherAI**: Fine-tuning with simple UI, no infrastructure
- **Self-hosting**: A100 GPU rental (~$1,800/mo at 70% utilization)

## How Our Tools Will Talk to Open WebUI

### Architecture Flow

```
User Message → Open WebUI → LangServe Chat Agent → LangGraph Planner → TypeScript Tools → Database/APIs
```

### Key Integration Points

1. **LangServe Server** (`apps/langserve/src/server.ts`)

   - Exposes existing TypeScript tools as REST API endpoints
   - Open WebUI sends chat requests to `http://langserve:7000/chat`
   - Returns OpenAI-compatible responses with tool results

2. **Tool Export Layer** (`apps/langserve/src/tools/exporter.ts`)

   ```typescript
   // Export existing tools to LangServe format (with proper naming)
   export async function exportTools() {
     return [
       new DataHubTool(db, supabaseUrl, supabaseKey), // SQL queries
       new VectorSearchTool(pineconeClient, openai, index), // Vector search
       new AnalysisTool(dataHubTool), // Analytics
       new MarketTool(dataHubTool), // Market data
       // All existing cannabis-specific tools
     ];
   }

   // Validate tool names match OpenAI regex
   function validateToolNames(tools: Tool[]) {
     const regex = /^[a-zA-Z0-9_-]{1,64}$/;
     tools.forEach((tool) => {
       if (!regex.test(tool.name)) {
         throw new Error(
           `Tool name '${tool.name}' violates OpenAI naming pattern`
         );
       }
     });
   }
   ```

3. **LangGraph Planner** (`apps/langserve/src/planners/LangGraphPlanner.ts`)

   - Receives user message from Open WebUI
   - Creates multi-step execution plan
   - Orchestrates tool calls in proper sequence
   - Returns formatted response to Open WebUI

4. **Open WebUI Configuration**

   ```yaml
   # webui/config/webui.yaml
   models:
     - name: "openai/gpt-4o-mini"
       base_url: "https://api.openai.com/v1"
     - name: "openai/gpt-3.5-turbo"
       base_url: "https://api.openai.com/v1"

   # Custom functions (our tools)
   functions:
     - name: "cannabis_assistant"
       url: "http://langserve:7000"
   ```

### Database Changes Required

Based on my analysis, here are the database changes needed:

#### 1. Chat Tables (Already Exist - No Changes)

- `chats` table: Stores chat sessions
- `messages` table: Stores individual messages
- `chat_attachments` table: Stores file uploads
- `agents` table: Stores agent configurations

#### 2. Environment Variables to Add

```bash
# Add to .env and docker-compose.yml
LANGSERVE_PORT=7000
WEBUI_PORT=3002
WEBUI_SECRET_KEY=your_webui_secret
WEBUI_ORIGIN=http://localhost:3002
```

#### 3. Configuration Changes

```typescript
// apps/platform/src/config/env.ts - Add to Env interface
interface Env {
  // ... existing fields
  langserve?: {
    port: number;
    host: string;
  };
  webui?: {
    port: number;
    origin: string;
    secretKey: string;
  };
}

// Update env loader
langserve: {
  port: parseInt(process.env.LANGSERVE_PORT || "7000"),
  host: process.env.LANGSERVE_HOST || "localhost",
},
webui: {
  port: parseInt(process.env.WEBUI_PORT || "3002"),
  origin: process.env.WEBUI_ORIGIN || "http://localhost:3002",
  secretKey: process.env.WEBUI_SECRET_KEY!,
}
```

### Authentication Integration

#### 1. WebUI Authentication Bridge

```typescript
// apps/langserve/src/auth/webui-bridge.ts
export class WebUIAuthBridge {
  static async validateSession(token: string, locationId: number) {
    // Validate Firebase JWT token
    const decoded = await admin.auth().verifyIdToken(token);

    // Get user from location database
    const user = await getUserFromClientId(locationId, {
      auth_id: decoded.sub,
    });

    return { user, locationId };
  }
}
```

#### 2. Open WebUI Custom Authentication

```javascript
// webui/auth/custom_auth.js
async function authenticateUser(token, locationId) {
  const response = await fetch("http://langserve:7000/auth/validate", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ token, locationId }),
  });

  return response.json();
}
```

### API Routes That Need Modification

#### 1. Chat Routes - Keep as Proxy/Bridge

```typescript
// apps/platform/src/chats/ChatController.ts - Modify to proxy to LangServe
export class ChatController {
  async sendMessage(ctx) {
    // Proxy to LangServe instead of SmokeyAIService
    const response = await fetch("http://langserve:7000/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        message: payload.message,
        chat_id: payload.chat_id,
        location_id: ctx.state.location.id,
        user_id: ctx.state.user?.id,
      }),
    });

    return response.json();
  }
}
```

#### 2. Public Product Controller - Add WebUI Proxy

```typescript
// apps/platform/src/products/PublicProductController.ts
router.get("/chat", async (ctx) => {
  // Redirect to Open WebUI with auth token
  const token = generateWebUIToken(ctx.state.locationId, ctx.state.user);
  ctx.redirect(
    `http://webui:3002/chat?token=${token}&locationId=${ctx.state.locationId}`
  );
});
```

### User Management & Admin Architecture

### Current Multi-Tenant Structure

Your platform has a sophisticated multi-tenant architecture:

- **Organizations** → **Locations** → **Users/Admins**
- **Role-based access**: owner, admin, member, support
- **Location-specific data isolation**
- **API keys per location** for public access

### Open WebUI Integration Strategy

#### Option 1: Embedded iframe (Recommended)

```
Your React App → Open WebUI iframe → LangServe → Your Tools
```

**User Management:**

- Keep existing Firebase auth in your React app
- Pass user context to Open WebUI via postMessage API
- No separate user accounts in Open WebUI needed

**Admin View:**

- Full chat monitoring through your existing dashboard
- All chat data stored in your `chats` and `messages` tables
- Usage analytics via your existing `ChatAnalyticsService`

#### Option 2: Separate Open WebUI instance per location

```
Location 1 → Open WebUI instance 1 → LangServe 1 → Tools (location_id=1)
Location 2 → Open WebUI instance 2 → LangServe 2 → Tools (location_id=2)
```

**Pros:** Complete isolation, easier to customize per location
**Cons:** More infrastructure complexity, harder to monitor centrally

### How Our Tools Will Talk to Open WebUI

#### Architecture Flow

```
User Message → Open WebUI → LangServe /chat → LangGraph Planner → TypeScript Tools → Database/APIs
```

#### Key Integration Points

1. **LangServe Chat Agent** - Main entry point that preserves user/location context
2. **Tool Context Injection** - Every tool call gets `location_id` and `user_id`
3. **Database Bridge** - Tools query your existing MySQL database
4. **Admin Dashboard Integration** - All chats flow through your existing monitoring

### Database Changes Required

```sql
-- Add chat tracking to existing tables
ALTER TABLE chats ADD COLUMN webui_chat_id VARCHAR(255);
ALTER TABLE chats ADD COLUMN webui_user_id VARCHAR(255);
ALTER TABLE messages ADD COLUMN webui_message_id VARCHAR(255);

-- Add usage tracking
CREATE TABLE chat_usage_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  location_id INT NOT NULL,
  user_id INT,
  chat_id VARCHAR(255),
  tokens_used INT DEFAULT 0,
  cost_usd DECIMAL(10,6) DEFAULT 0,
  model_used VARCHAR(100),
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_location_timestamp (location_id, timestamp),
  INDEX idx_cost_tracking (location_id, timestamp, cost_usd)
);

-- Add admin monitoring
CREATE TABLE admin_chat_sessions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  admin_id INT NOT NULL,
  location_id INT NOT NULL,
  webui_session VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Admin Monitoring Dashboard

You'll get comprehensive admin oversight through your existing dashboard:

```typescript
// Enhanced dashboard with chat usage metrics
const ChatUsageMetrics = {
  totalChats: 1247,
  activeUsers: 89,
  totalTokensUsed: 2450000,
  totalCostThisMonth: 156.78,
  averageChatLength: 8.3,
  mostUsedModel: "groq/llama-3-8b-instant",
  topUsers: [
    { name: "<EMAIL>", chats: 45, cost: 12.34 },
    { name: "<EMAIL>", chats: 38, cost: 9.87 },
  ],
};
```

## Cost Optimization Plan

#### Current Strategy (Phase 1)

- **Inference**: OpenAI API (keep existing integration)
- **Cost**: ~$0.03-0.06 per 1K tokens
- **Simplicity**: No infrastructure management needed

#### Future Strategy (Phase 2)

- **Inference**: Groq Cloud Llama-3 8B Instant ($0.13/Mtok)
- **Fine-tuning**: TogetherAI (simple UI, no infrastructure)
- **Cost savings**: 50-70% reduction vs OpenAI
- **Target**: <$0.01 per chat interaction

#### Migration Path

1. **Keep OpenAI API** for immediate implementation
2. **Add Groq Cloud** as fallback/alternative model
3. **Use TogetherAI** for fine-tuning when needed
4. **Monitor costs** and switch to Groq when beneficial

## Migration Timeline

| Week | Phase          | Deliverables                        |
| ---- | -------------- | ----------------------------------- |
| 1    | Infrastructure | Docker services, LangServe scaffold |
| 2    | Backend        | Tool export, LangGraph planner      |
| 3    | Frontend       | WebUI integration, auth flow        |
| 4    | Observability  | Langfuse, Phoenix setup             |
| 5    | Optimization   | Fine-tuning, load testing           |

## Risk Mitigation

### Technical Risks

- **OpenAI API costs**: Monitor usage, implement caching for repeated queries
- **Pillow upload issues**: Test file upload thoroughly, have fallback
- **LangServe integration**: Maintain backward compatibility during transition
- **Groq Cloud availability**: Keep OpenAI as fallback during transition

### Business Risks

- **User experience**: Gradual rollout with feature flags
- **Performance**: Load test before production deployment
- **Cost overruns**: Monitor usage, set budget alerts

## Success Metrics

### Technical Metrics

- Response time: P95 < 750ms
- Uptime: >99.9%
- Error rate: <1%
- File upload success: >99%

### Business Metrics

- User adoption: >90% of existing users
- Cost per interaction: $0.03 (realistic with GPT-4o mini)
- Customer satisfaction: Maintain or improve current scores

## Validation Gates (Updated)

### Syntax & Types

```bash
# TypeScript compilation
pnpm tsc --noEmit

# ESLint with OpenAI function name validation
pnpm eslint "apps/**/*.{ts,tsx}" --fix

# Add ESLint rule for OpenAI function names
# .eslintrc.js
{
  "rules": {
    "custom/openai-function-names": ["error", {
      "pattern": "^[a-zA-Z0-9_-]{1,64}$"
    }]
  }
}

# LangServe contract validation
curl -f http://localhost:7000/docs | jq '.paths."/chat"'
```

### Unit Tests

```bash
# Run all tests
pnpm vitest run

# Test tool name validation
pnpm vitest run apps/langserve/src/__tests__/tool-validation.test.ts

# Test specific components
pnpm vitest run apps/langserve/src/__tests__/
```

### Integration Tests

```bash
# Test chat flow
npx zx scripts/test-chat-flow.mjs

# Test file upload (CRITICAL - includes JPEG support)
pnpm jest e2e-upload

# Test model switching
npx zx scripts/test-model-switch.mjs
```

### Load Testing

```bash
# 50 virtual users, P95 < 750ms
k6 run load/chat.lua --vus 50 --duration 5m

# Stream response time < 1s for first token
npx zx scripts/test-stream.mjs
```

### E2E Tests

```bash
# Full chat workflow
npm run test:e2e:chat

# File upload workflow with JPEG test
npm run test:e2e:upload

# Model switching workflow
npm run test:e2e:model-switch
```

### Security Tests

```bash
# Test iframe origin validation
npm run test:security:iframe

# Test postMessage authentication
npm run test:security:postmessage

# Test Cross-Origin policies
npm run test:security:cors
```

## References

### Documentation

- [Open WebUI Docs](https://docs.openwebui.com/features/workspace/models/)
- [LangServe Documentation](https://python.langchain.com/docs/langserve/)
- [LangGraph Tutorial](https://langchain-ai.github.io/langgraph/tutorials/plan-and-execute/)
- [OpenAI API Docs](https://platform.openai.com/docs/api-reference)
- [Groq Cloud Docs](https://console.groq.com/docs)
- [TogetherAI Docs](https://docs.together.ai/)
- [Langfuse Tracing](https://langfuse.com/docs/tracing)

### Codebase References

- `apps/platform/src/chats/SmokeyAIService.ts` - Current monolithic service
- `apps/platform/src/tools/` - Existing LangChain tools
- `apps/ui/src/views/chat/ModernChat_new.tsx` - Current React chat UI
- `docker-compose.yml` - Current infrastructure
- `apps/platform/src/chats/ChatWebSocket.ts` - WebSocket implementation

### External Examples

- [Open WebUI GitHub](https://github.com/open-webui/open-webui)
- [LangServe Examples](https://github.com/langchain-ai/langserve)
- [Groq Cloud Examples](https://github.com/groq/groq-python)
- [TogetherAI Examples](https://github.com/togethercomputer/together-python)
- [Langfuse Python SDK](https://github.com/langfuse/langfuse-python)
