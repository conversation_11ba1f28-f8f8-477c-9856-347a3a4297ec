# Supabase to CannMenus Replacement Analysis

## Overview

This document analyzes which Supabase functions can be replaced with CannMenus API and provides a migration strategy for complete replacement.

## CannMenus API Capabilities Analysis

Based on the OpenAPI specification, CannMenus provides:

### ✅ Available Endpoints
1. **`/v1/products`** - Product search with extensive filtering
2. **`/v1/products/meta`** - Product search by meta SKU
3. **`/v1/retailers`** - Retailer/dispensary search
4. **`/v1/brands`** - Brand search

### 🔍 Available Parameters
- **Geographic filtering**: `lat`, `lng`, `distance` 
- **Location filtering**: `states`, `city`, `zipcode`
- **Product filtering**: `category`, `subcategory`, `brand_name`, `product_name`
- **Advanced filtering**: THC/CBD levels, pricing, medical/recreational
- **Search**: General query search across fields
- **Pagination**: `page` parameter for large datasets

## Supabase Function Replacement Analysis

### 🟢 **CAN BE REPLACED** - Direct CannMenus Equivalents

| Supabase Function | CannMenus Replacement | Migration Status |
|-------------------|----------------------|------------------|
| `searchRetailers()` | `/v1/retailers?name=query` | ✅ **REPLACEABLE** |
| `searchRetailersByNameOnly()` | `/v1/retailers?name=query` | ✅ **REPLACEABLE** |
| `searchRetailersByCityState()` | `/v1/retailers?city=X&state=Y` | ✅ **REPLACEABLE** |
| `searchRetailersByLocationOnly()` | `/v1/retailers?city=X&state=Y` | ✅ **REPLACEABLE** |
| `searchRetailersByLocation()` | `/v1/retailers?city=X&state=Y&zipcode=Z` | ✅ **REPLACEABLE** |
| `searchNearbyRetailers()` | `/v1/retailers?lat=X&lng=Y&distance=Z` | ✅ **REPLACEABLE** |
| `getRetailerById()` | `/v1/retailers?id=X` | ✅ **REPLACEABLE** |
| `getRetailerProducts()` | `/v1/products?retailers=[id]` | ✅ **REPLACEABLE** |
| `getRetailerProductCount()` | `/v1/products?retailers=[id]` (count results) | ✅ **REPLACEABLE** |
| **Brand Search (supabaseTools.ts)** | `/v1/brands?name=X` | ✅ **REPLACEABLE** |
| **Product Search (supabaseTools.ts)** | `/v1/products?q=query&category=X&brand_name=Y` | ✅ **REPLACEABLE** |
| **Product Analysis (supabaseTools.ts)** | `/v1/products` with aggregation | ✅ **REPLACEABLE** |

### 🟡 **PARTIALLY REPLACEABLE** - Requires Custom Logic

| Supabase Function | CannMenus Approach | Migration Notes |
|-------------------|-------------------|-----------------|
| `getMarketSnapshotData()` | Multiple `/v1/products` calls + aggregation | Need client-side analytics |
| `getRegionalMarketDataByLocation()` | `/v1/retailers` + `/v1/products` + aggregation | Multi-step process |
| `getRegionalMarketDataByRadius()` | `/v1/retailers?lat&lng&distance` + `/v1/products` | Geographic + product data |
| **Market Analysis (supabaseTools.ts)** | Batch `/v1/products?retailers=[]` + analytics | Already implemented! |
| **Competitor Analysis (supabaseTools.ts)** | Use existing `CannMenusApiService.getBatchCompetitorData()` | Already implemented! |

### 🔴 **CANNOT BE REPLACED** - No CannMenus Equivalent

| Supabase Function | Reason | Alternative Solution |
|-------------------|--------|---------------------|
| **Storage Functions** | CannMenus has no file storage | Use Firebase Storage, AWS S3, or keep Supabase Storage |
| **Upload/Download Files** | Not a CannMenus feature | External storage service needed |
| **Event Management** | CannMenus doesn't handle events | Keep in MySQL or separate service |
| **Document Management** | CannMenus doesn't handle documents | External document service needed |
| **User Data Management** | CannMenus is product/retailer focused | Keep in MySQL or separate service |

## 📋 Migration Strategy

### Phase 1: Replace Supabase Tools (HIGH PRIORITY)
**Files to Update:**
- `apps/platform/src/chats/tools/supabaseTools.ts`
- Update all tool functions to use CannMenus API instead of Supabase

### Phase 2: Update CompetitorService (MEDIUM PRIORITY)  
**Files to Update:**
- `apps/platform/src/competitors/CompetitorService.ts`
- Replace SupabaseService calls with CannMenusApiService calls

### Phase 3: Handle Non-Replaceable Functions (LOW PRIORITY)
**Files to Update:**
- Storage-related files: Keep Supabase or migrate to alternative
- Event management: Keep current system or migrate to MySQL

## 🚀 Implementation Plan

### Step 1: Create CannMenus Tool Replacements

Replace all functions in `supabaseTools.ts` with CannMenus equivalents:

```typescript
// OLD: Supabase-based
export const RetailerSearchTool = tool(async ({ query, limit }) => {
  const retailers = await supabaseService.searchRetailers(query, limit);
  return JSON.stringify({ retailers, count: retailers.length });
});

// NEW: CannMenus-based  
export const RetailerSearchTool = tool(async ({ query, limit }) => {
  const retailers = await cannMenusApiService.getRetailers({ name: query });
  return JSON.stringify({ retailers, count: retailers.length });
});
```

### Step 2: Update CompetitorService

Replace SupabaseService dependency:

```typescript
// OLD: Uses SupabaseService
constructor() {
  this.supabaseService = new SupabaseService({...});
}

// NEW: Uses CannMenusApiService
constructor() {
  this.cannMenusApiService = cannMenusApiService;
}
```

### Step 3: Update SmokeyAIService Tool Mappings

Remove Supabase tools and use CannMenus tools:

```typescript
// OLD: Mixed Supabase and CannMenus tools
const tools = [
  ...supabaseTools,  // Remove this
  ...cannMenusTools, // Keep this
];

// NEW: CannMenus tools only
const tools = [
  ...cannMenusTools,
];
```

## 📊 Migration Benefits

### ✅ Advantages of Full CannMenus Migration
1. **Single API Provider** - Simplified architecture
2. **Reduced Dependencies** - No Supabase maintenance
3. **Cost Optimization** - Single billing source  
4. **Performance** - Already optimized CannMenus integration
5. **Consistency** - All market data from one source

### ⚠️ Migration Challenges
1. **Storage Replacement** - Need alternative for file storage
2. **Custom Analytics** - Some functions need client-side aggregation
3. **Testing Required** - Ensure feature parity
4. **Data Migration** - Historical data considerations

## 🎯 Recommendation

**PROCEED WITH MIGRATION** for all replaceable functions:

### High Priority (Immediate)
- ✅ Replace `supabaseTools.ts` with CannMenus equivalents
- ✅ Update CompetitorService to use CannMenus  
- ✅ Remove Supabase dependencies from chat tools

### Medium Priority (Next Phase)
- 🔄 Evaluate storage alternatives (Firebase/AWS S3)
- 🔄 Migrate event management to MySQL
- 🔄 Consider keeping minimal Supabase for non-replaceable functions

### Result
**~80% of Supabase usage can be replaced** with CannMenus API, leaving only storage and specialized functions.

## ⚡ Next Steps

1. **Review this analysis** with team
2. **Choose storage alternative** (Firebase, AWS S3, etc.)
3. **Begin Phase 1 implementation** (supabaseTools.ts replacement)
4. **Test migration incrementally** to ensure no functionality loss
5. **Update environment variables** and deployment configs