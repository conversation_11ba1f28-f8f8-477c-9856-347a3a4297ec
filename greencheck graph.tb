graph TB
    A[User starts onboarding] --> B[Select Green Check in POS section]
    B --> C[Green Check dialog opens]
    C --> D[Authenticate with sandbox credentials]
    D --> E[Fetch available CRBs from Green Check API]
    E --> F[User selects CRB location]
    F --> G[Validate CRB connection]
    G --> H[Preview data samples]
    H --> I[Confirm connection]
    I --> J[Store CRB ID in location.pos_location_id]
    J --> K[Complete onboarding]
    
    K --> L[Green Check Provider ready for data sync]
    L --> M[Can sync inventory, sales, customers]
    
    subgraph "Green Check API Flow"
        D1[POST /auth with client_id/secret] --> D2[GET /service-providers/sp_id/crbs]
        D2 --> D3[POST /crb/crb_id/validate]
        D3 --> D4[GET /crb/crb_id/preview]
    end
    
    subgraph "Database Integration"
        J1[locations.pos_location_id = CRB ID]
        J2[GreenCheckProvider uses CRB ID for sync]
        J3[Multiple POS systems via Green Check]
    end