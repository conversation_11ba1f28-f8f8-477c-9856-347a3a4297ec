#!/usr/bin/env node

/**
 * Create Test Admin and Test Document Upload
 * This script creates a test admin user, generates an auth token, and tests document upload
 */

require("dotenv").config();
const axios = require("axios");
const FormData = require("form-data");
const fs = require("fs");
const path = require("path");
const knex = require("knex");
const { sign } = require("jsonwebtoken");

// Database configuration
const dbConfig = {
  client: "mysql2",
  connection: {
    host: "localhost", // Use localhost since we're connecting from outside Docker
    port: parseInt(process.env.DB_PORT || "3306"),
    user: process.env.DB_USERNAME || "root",
    password: process.env.DB_PASSWORD || "password",
    database: process.env.DB_DATABASE || "marketing_auto",
  },
};

const API_BASE = "http://localhost:3001/api/admin";
const SAMPLE_DIR =
  "/Users/<USER>/Development/Applications/marketing_auto/Sample Data";

function log(message, type = "info") {
  const timestamp = new Date().toISOString();
  const prefix =
    {
      info: "📄",
      success: "✅",
      error: "❌",
      warning: "⚠️",
      debug: "🔍",
    }[type] || "📄";

  console.log(`${prefix} [${timestamp}] ${message}`);
}

async function createTestAdmin() {
  const db = knex(dbConfig);

  try {
    log("Creating test admin user and organization...", "info");

    // First, create or get organization
    let organization;
    try {
      [organization] = await db("organizations")
        .where("username", "test-org")
        .limit(1);
      if (!organization) {
        await db("organizations").insert({
          username: "test-org",
          domain: "test.com",
          created_at: new Date(),
          updated_at: new Date(),
        });

        // For MySQL, we need to get the inserted record
        [organization] = await db("organizations")
          .where("username", "test-org")
          .limit(1);
      }
      log(
        `Organization: ${organization.username} (ID: ${organization.id})`,
        "success"
      );
    } catch (error) {
      log(`Error creating organization: ${error.message}`, "error");
      throw error;
    }

    // Create or get admin user
    let admin;
    try {
      [admin] = await db("admins").where("email", "<EMAIL>").limit(1);
      if (!admin) {
        await db("admins").insert({
          email: "<EMAIL>",
          first_name: "Test",
          last_name: "Admin",
          role: "owner",
          organization_id: organization.id,
          created_at: new Date(),
          updated_at: new Date(),
        });

        [admin] = await db("admins")
          .where("email", "<EMAIL>")
          .limit(1);
      }
      log(`Admin: ${admin.email} (ID: ${admin.id})`, "success");
    } catch (error) {
      log(`Error creating admin: ${error.message}`, "error");
      throw error;
    }

    // Generate JWT token
    const secret =
      process.env.APP_SECRET ||
      process.env.SECRET ||
      "Ck7R453k4j5k435ndjymbBoA6zqih";
    const expiresAt = Math.floor(Date.now() / 1000) + 24 * 60 * 60; // 24 hours

    const token = sign(
      {
        id: admin.id,
        organization_id: admin.organization_id,
        role: admin.role,
        exp: expiresAt,
      },
      secret
    );

    log(`Generated JWT token: ${token.substring(0, 30)}...`, "success");

    // Store token in database
    await db("access_tokens").insert({
      admin_id: admin.id,
      token: token,
      expires_at: new Date(expiresAt * 1000),
      revoked: false,
      ip: "127.0.0.1",
      user_agent: "test-script",
      created_at: new Date(),
      updated_at: new Date(),
    });

    await db.destroy();

    return { admin, organization, token };
  } catch (error) {
    await db.destroy();
    throw error;
  }
}

async function createTestLocation(organizationId, adminId) {
  const db = knex(dbConfig);

  try {
    // Check if test location already exists
    let [location] = await db("locations")
      .where("organization_id", organizationId)
      .where("name", "Test Location")
      .limit(1);

    if (!location) {
      // Create test location
      await db("locations").insert({
        organization_id: organizationId,
        name: "Test Location",
        description: "Test location for document upload",
        locale: "en",
        timezone: "America/New_York",
        created_at: new Date(),
        updated_at: new Date(),
      });

      [location] = await db("locations")
        .where("organization_id", organizationId)
        .where("name", "Test Location")
        .limit(1);

      // Add the admin to the location
      await db("location_admins").insert({
        location_id: location.id,
        admin_id: adminId,
        role: "admin",
        created_at: new Date(),
        updated_at: new Date(),
      });

      log(
        `Created test location: ${location.name} (ID: ${location.id})`,
        "success"
      );
    } else {
      log(
        `Using existing test location: ${location.name} (ID: ${location.id})`,
        "info"
      );
    }

    await db.destroy();
    return location;
  } catch (error) {
    await db.destroy();
    throw error;
  }
}

async function testDocumentUploadWithAuth(token, locationId) {
  log("Testing document upload with authentication...", "info");

  const testFile = path.join(
    SAMPLE_DIR,
    "Cannabis_Business_Development_Plan.pdf"
  );

  if (!fs.existsSync(testFile)) {
    log(`Test file not found: ${testFile}`, "error");
    return false;
  }

  log(`Using test file: ${testFile}`, "info");
  log(`Using location ID: ${locationId}`, "info");

  const form = new FormData();
  form.append("file", fs.createReadStream(testFile));

  try {
    const response = await axios.post(
      `${API_BASE}/locations/${locationId}/documents/upload`,
      form,
      {
        headers: {
          ...form.getHeaders(),
          Authorization: `Bearer ${token}`,
        },
        timeout: 30000,
        validateStatus: function (status) {
          return status < 500; // Don't throw for 4xx errors
        },
      }
    );

    log(`Upload response status: ${response.status}`, "info");
    log(`Upload response: ${JSON.stringify(response.data, null, 2)}`, "info");

    if (response.status === 200) {
      log("✅ Document upload successful!", "success");

      if (response.data.document_id) {
        log(`Document ID: ${response.data.document_id}`, "success");

        // Wait a moment and check document processing status
        await new Promise((resolve) => setTimeout(resolve, 3000));
        await checkDocumentStatus(response.data.document_id, token, locationId);
      }

      return true;
    } else if (response.status === 401) {
      log("Authentication still failed - token may be invalid", "error");
      return false;
    } else {
      log(`Upload failed with status: ${response.status}`, "error");
      return false;
    }
  } catch (error) {
    log(`Upload error: ${error.message}`, "error");
    return false;
  }
}

async function checkDocumentStatus(documentId, token, locationId) {
  log(`Checking document processing status for ID: ${documentId}`, "info");

  try {
    const response = await axios.get(
      `${API_BASE}/locations/${locationId}/documents/${documentId}/analysis`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        timeout: 10000,
        validateStatus: function (status) {
          return status < 500;
        },
      }
    );

    if (response.status === 200) {
      log("Document status retrieved successfully", "success");
      log(`Status: ${response.data.document?.status || "unknown"}`, "info");

      if (response.data.analysis) {
        log("✅ Document has been analyzed", "success");
      }

      if (response.data.vectorization) {
        log("✅ Document has been vectorized for search", "success");
      }

      return true;
    } else {
      log(`Status check failed: ${response.status}`, "warning");
      return false;
    }
  } catch (error) {
    log(`Status check error: ${error.message}`, "warning");
    return false;
  }
}

async function testDocumentSearch(token, locationId) {
  log("Testing document search after upload...", "info");

  // Simulate a search query that would match the uploaded document
  try {
    const response = await axios.get(
      `${API_BASE}/locations/${locationId}/documents/search?query=cannabis business plan`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        timeout: 10000,
        validateStatus: function (status) {
          return status < 500;
        },
      }
    );

    if (response.status === 200) {
      log("✅ Document search successful", "success");
      log(`Found ${response.data.results?.length || 0} results`, "info");

      if (response.data.results && response.data.results.length > 0) {
        log("Sample search results:", "info");
        response.data.results.slice(0, 2).forEach((result, index) => {
          log(
            `  ${index + 1}. ${result.document_name} (chunk ${
              result.chunk_index + 1
            })`,
            "info"
          );
        });
      }

      return true;
    } else {
      log(`Search failed: ${response.status}`, "warning");
      return false;
    }
  } catch (error) {
    log(`Search error: ${error.message}`, "warning");
    return false;
  }
}

async function runCompleteTest() {
  log("🚀 Starting Complete Document Upload & Search Test", "info");
  log("=" * 60, "info");

  try {
    // Step 1: Create test admin and get auth token
    const { admin, organization, token } = await createTestAdmin();

    log("", "info");
    log("✅ Authentication Setup Complete", "success");
    log(`Admin: ${admin.email}`, "success");
    log(`Organization: ${organization.username}`, "success");

    // Step 2: Create test location
    log("", "info");
    const location = await createTestLocation(organization.id, admin.id);

    // Step 3: Test document upload
    log("", "info");
    const uploadSuccess = await testDocumentUploadWithAuth(token, location.id);

    if (uploadSuccess) {
      log("✅ Document upload is working!", "success");

      // Step 4: Test document search
      log("", "info");
      const searchSuccess = await testDocumentSearch(token, location.id);

      if (searchSuccess) {
        log("✅ Document search is working!", "success");
      }
    }

    log("", "info");
    log("🎯 Test Summary:", "info");
    log(`✅ Firebase storage configuration: FIXED`, "success");
    log(
      `✅ Authentication: ${uploadSuccess ? "WORKING" : "NEEDS ATTENTION"}`,
      uploadSuccess ? "success" : "warning"
    );
    log(
      `✅ Document upload: ${uploadSuccess ? "WORKING" : "NEEDS ATTENTION"}`,
      uploadSuccess ? "success" : "warning"
    );
    log(
      `✅ Document processing: ${uploadSuccess ? "READY" : "NEEDS TESTING"}`,
      uploadSuccess ? "success" : "warning"
    );

    log("", "info");
    log("📋 Next Steps:", "info");
    log("1. Upload more documents via the UI", "info");
    log("2. Test document context in chat responses", "info");
    log("3. Verify document citations in AI responses", "info");
  } catch (error) {
    log(`Test failed: ${error.message}`, "error");
    console.error(error);
  }
}

// Run the test
if (require.main === module) {
  runCompleteTest();
}

module.exports = {
  createTestAdmin,
  testDocumentUploadWithAuth,
  checkDocumentStatus,
  testDocumentSearch,
};
