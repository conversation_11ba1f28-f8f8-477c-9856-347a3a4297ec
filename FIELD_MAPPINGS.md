# POS Partner Data Field Mappings

This document shows how POS partner data fields map to our existing database schema.

## User/Customer Field Mappings

| POS Partner Field | Our Database Field | Table | Notes                               |
| ----------------- | ------------------ | ----- | ----------------------------------- |
| `email_address`   | `email`            | users | ✅ Existing field                   |
| `phone`           | `phone`            | users | ✅ Existing field                   |
| `cell_phone`      | `cell_phone`       | users | ✅ New field added                  |
| `dob`             | `birth_date`       | users | ✅ Existing field                   |
| `first_name`      | `first_name`       | users | ✅ Moved from JSON to direct column |
| `last_name`       | `last_name`        | users | ✅ Moved from JSON to direct column |
| `middle_name`     | `middle_name`      | users | ✅ New field added                  |
| `address_1`       | `address_1`        | users | ✅ New field added                  |
| `address_2`       | `address_2`        | users | ✅ New field added                  |
| `city`            | `city`             | users | ✅ New field added                  |
| `state`           | `state`            | users | ✅ New field added                  |
| `zip_code`        | `zip_code`         | users | ✅ New field added                  |
| `pos_customer_id` | `pos_customer_id`  | users | ✅ New field added                  |
| `crb_id`          | `crb_id`           | users | ✅ New field added                  |
| `pos_type`        | `pos_type`         | users | ✅ New field added                  |
| `loyalty_points`  | `loyalty_points`   | users | ✅ New field added                  |
| `loyalty_member`  | `loyalty_member`   | users | ✅ New field added                  |

## Product Field Mappings

| POS Partner Field              | Our Database Field         | Table    | Notes                           |
| ------------------------------ | -------------------------- | -------- | ------------------------------- |
| `name`                         | `product_name`             | products | ✅ Existing field               |
| `pos_product_category_name`    | `category`                 | products | ✅ Use existing field           |
| `pos_product_subcategory_name` | `subcategory`              | products | ✅ Use existing field           |
| `pos_strain_name`              | `cultivar`                 | products | ✅ Use existing field           |
| `pos_brand_name`               | `brand_name`               | products | ✅ Use existing field           |
| `medicated`                    | `medical`                  | products | ✅ Use existing field (tinyint) |
| `sku`                          | `meta_sku`                 | products | ✅ Use existing field           |
| `description`                  | `product_description`      | products | ✅ Use existing field           |
| `images`                       | `images_urls`              | products | ✅ Use existing field           |
| `price`                        | `latest_price`             | products | ✅ Use existing field           |
| `unit_cost`                    | `wholesale_price`          | products | ✅ Use existing field           |
| `pos_updated_date_local`       | `updated_at`               | products | ✅ Use existing field           |
| `pos_product_id`               | `pos_product_id`           | products | ✅ New field added              |
| `crb_id`                       | `crb_id`                   | products | ✅ New field added              |
| `pos_type`                     | `pos_type`                 | products | ✅ New field added              |
| `gc_product_category_name`     | `gc_product_category_name` | products | ✅ New field added              |
| `gc_net_weight_grams`          | `gc_net_weight_grams`      | products | ✅ New field added              |
| `pos_unit_of_measure`          | `pos_unit_of_measure`      | products | ✅ New field added              |
| `price_tiers`                  | `price_tiers`              | products | ✅ New field added (JSON)       |
| `pos_updated_date`             | `pos_updated_date`         | products | ✅ New field added              |
| `gc_created_date`              | `gc_created_date`          | products | ✅ New field added              |
| `gc_created_date_local`        | `gc_created_date_local`    | products | ✅ New field added              |

## POS Data Field Mappings

| POS Partner Field          | Our Database Field   | Table    | Notes                                           |
| -------------------------- | -------------------- | -------- | ----------------------------------------------- |
| `customer.customer_id`     | `customer_id`        | pos_data | ✅ Existing field (added in previous migration) |
| `customer.pos_customer_id` | `customer_id`        | pos_data | ✅ Same as above                                |
| `customer.dob`             | Maps to users table  | users    | ✅ Via customer lookup                          |
| `subtotal`                 | `gross_sales`        | pos_data | ✅ Existing field                               |
| `total_discounts`          | `discounted_amount`  | pos_data | ✅ Existing field                               |
| `tax_paid`                 | `tax_amount`         | pos_data | ✅ Existing field                               |
| `total_paid`               | `invoice_total`      | pos_data | ✅ Existing field                               |
| `date`                     | `order_date`         | pos_data | ✅ Existing field                               |
| `pos_sale_id`              | Maps to order system | orders   | ✅ Via order integration                        |
| `employee_id`              | `budtender_name`     | pos_data | ✅ Existing field (may need mapping)            |
| `order_id`                 | `order_id`           | pos_data | ✅ New field added                              |

## Location Field Mappings

| POS Partner Field        | Our Database Field       | Table     | Notes                                     |
| ------------------------ | ------------------------ | --------- | ----------------------------------------- |
| `id`                     | `pos_location_id`        | locations | ✅ New field added (external POS ID)      |
| `name`                   | `name`                   | locations | ✅ Existing field                         |
| `street_address`         | `street_address`         | locations | ✅ New field (more specific than address) |
| `street_address_2`       | `street_address_2`       | locations | ✅ New field added                        |
| `city`                   | `city`                   | locations | ✅ Existing field                         |
| `state`                  | `state`                  | locations | ✅ Existing field                         |
| `postal_code`            | `postal_code`            | locations | ✅ New field (more specific than zip)     |
| `country`                | `country`                | locations | ✅ Existing field                         |
| `phone_number`           | `phone`                  | locations | ✅ Existing field                         |
| `website`                | `website`                | locations | ✅ Existing field                         |
| `timezone`               | `timezone`               | locations | ✅ Existing field                         |
| `ein`                    | `ein`                    | locations | ✅ New field added                        |
| `dba`                    | `dba`                    | locations | ✅ New field added                        |
| `entityType`             | `entity_type`            | locations | ✅ New field added                        |
| `business_type`          | `business_type`          | locations | ✅ New field added                        |
| `org_type`               | `org_type`               | locations | ✅ New field added                        |
| `established_date`       | `established_date`       | locations | ✅ New field added                        |
| `ftEmployees`            | `ft_employees`           | locations | ✅ New field added                        |
| `ptEmployees`            | `pt_employees`           | locations | ✅ New field added                        |
| `monthlySales`           | `monthly_sales`          | locations | ✅ New field added                        |
| `monthlyCustomers`       | `monthly_customers`      | locations | ✅ New field added                        |
| `status`                 | `status`                 | locations | ✅ New field added                        |
| `template_id`            | `template_id`            | locations | ✅ New field added                        |
| `template_result_id`     | `template_result_id`     | locations | ✅ New field added                        |
| `pos_configs`            | `pos_configs`            | locations | ✅ New field added (JSON array)           |
| `mailing_street_address` | `mailing_street_address` | locations | ✅ New field added                        |
| `mailing_city`           | `mailing_city`           | locations | ✅ New field added                        |
| `mailing_state`          | `mailing_state`          | locations | ✅ New field added                        |
| `mailing_postal_code`    | `mailing_postal_code`    | locations | ✅ New field added                        |

## Implementation Notes

### Existing Fields Usage

- **Products**: We have comprehensive product fields already, so we avoid duplicating them
- **Users**: We moved frequently-used name fields from JSON to direct columns for performance
- **POS Data**: Most transaction fields already exist from previous implementations

### New Fields Added

- **GC Prefixed Fields**: Green Cannabis specific fields that don't have equivalents
- **POS Integration Fields**: Fields needed for POS system integration and tracking
- **Cannabis Regulation**: Fields specific to cannabis industry compliance

### Data Migration Strategy

- Existing data remains in JSON fields for backwards compatibility
- New direct columns are populated via data migration
- Getters prioritize direct columns but fall back to JSON for smooth transition

## Inventory Field Mappings

| Green Check Field       | Our Database Field      | Table     | Notes                         |
| ----------------------- | ----------------------- | --------- | ----------------------------- |
| `product_id`            | `product_id`            | inventory | ✅ Internal product reference |
| `pos_product_id`        | `pos_product_id`        | inventory | ✅ POS system product ID      |
| `crb_id`                | `crb_id`                | inventory | ✅ Cannabis regulatory ID     |
| `quantity_on_hand`      | `quantity_on_hand`      | inventory | ✅ Physical inventory count   |
| `quantity_available`    | `quantity_available`    | inventory | ✅ Available for sale         |
| `grams_on_hand`         | `grams_on_hand`         | inventory | ✅ Weight-based tracking      |
| `unit_cost`             | `unit_cost`             | inventory | ✅ Cost per unit              |
| `unit_price`            | `unit_price`            | inventory | ✅ Selling price per unit     |
| `inventory_location_id` | `inventory_location_id` | inventory | ✅ Sub-location within store  |
| `batch_number`          | `batch_number`          | inventory | ✅ Cannabis batch tracking    |
| `thc_percentage`        | `thc_percentage`        | inventory | ✅ THC content                |
| `cbd_percentage`        | `cbd_percentage`        | inventory | ✅ CBD content                |
| `expiration_date`       | `expiration_date`       | inventory | ✅ Product expiration         |
| `status`                | `status`                | inventory | ✅ Active/inactive status     |

## Green Check API Integration

### Service Provider Architecture

- **Service Provider ID**: Identifies the specific POS system (e.g., BioTrack, Greenbits, Treez)
- **CRB ID**: Cannabis Regulatory Board ID - maps to our `location_id`
- **Authentication**: Bearer token authentication via API key

### API Endpoints Mapped

| Green Check Endpoint       | Our Data Sync  | Target Table | Provider Method   |
| -------------------------- | -------------- | ------------ | ----------------- |
| `/crbs/{crb_id}`           | Location data  | `locations`  | `syncLocation()`  |
| `/crbs/{crb_id}/customers` | Customer data  | `users`      | `syncCustomers()` |
| `/crbs/{crb_id}/products`  | Product data   | `products`   | `fetchProducts()` |
| `/crbs/{crb_id}/sales`     | Sales data     | `pos_data`   | `fetchTickets()`  |
| `/crbs/{crb_id}/inventory` | Inventory data | `inventory`  | `syncInventory()` |

### Sync Strategy

1. **Location Sync**: Update business information, POS configs, compliance status
2. **Customer Sync**: Create/update customer records with cannabis-specific data
3. **Product Sync**: Sync product catalog with strain, category, and regulatory data
4. **Inventory Sync**: Real-time inventory levels, quantities, and pricing
5. **Sales Sync**: Historical and real-time transaction data with compliance tracking

### Green Check Integration Benefits

- **Multi-POS Support**: Single integration point for multiple POS systems
- **Cannabis Compliance**: Built-in regulatory compliance tracking
- **Real-time Data**: Live inventory, sales, and customer synchronization
- **Unified Data Model**: Consistent data structure across different POS providers
